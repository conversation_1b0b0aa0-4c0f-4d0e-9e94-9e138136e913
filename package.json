{"name": "continew-admin-ui", "type": "module", "version": "4.0.0-SNAPSHOT", "private": "true", "scripts": {"bootstrap": "pnpm install --registry=https://registry.npmmirror.com", "dev": "vite --host", "build": "vue-tsc --noEmit && vite build", "build:test": "vue-tsc --noEmit && vite build --mode test", "preview": "vite preview --port 5050", "typecheck": "vue-tsc --noEmit", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@arco-design/color": "^0.4.0", "@arco-themes/vue-gi-demo": "^0.0.51", "@codemirror/lang-javascript": "^6.2.1", "@codemirror/lang-vue": "^0.1.2", "@codemirror/theme-one-dark": "^6.1.2", "@ddietr/codemirror-themes": "^1.4.2", "@editorjs/editorjs": "^2.30.8", "@editorjs/header": "^2.8.8", "@editorjs/list": "^2.0.8", "@vue-office/docx": "1.6.0", "@vue-office/excel": "1.7.1", "@vue-office/pdf": "1.6.4", "@vueuse/components": "^10.5.0", "@vueuse/core": "^10.5.0", "aieditor": "^1.0.13", "animate.css": "^4.1.1", "axios": "^0.27.2", "codemirror": "^6.0.1", "cron-parser": "^4.9.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.4", "echarts": "^5.4.2", "editorjs-component-vue": "^0.0.3", "jsencrypt": "^3.3.2", "lint-staged": "^15.2.10", "lodash-es": "^4.17.21", "mitt": "^3.0.0", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "pinia": "^2.0.16", "pinia-plugin-persistedstate": "^3.1.0", "qs": "^6.11.2", "query-string": "^9.0.0", "v-viewer": "^3.0.10", "viewerjs": "^1.11.6", "vite-plugin-vue-devtools": "^7.0.27", "vue": "^3.5.4", "vue-codemirror6": "^1.1.27", "vue-color-kit": "^1.0.5", "vue-cropper": "^1.1.1", "vue-demi": "^0.14.10", "vue-draggable-plus": "^0.3.5", "vue-echarts": "^6.5.5", "vue-i18n": "^9.14.4", "vue-json-pretty": "^2.4.0", "vue-router": "^4.3.3", "vue3-tree-org": "^4.2.2", "xe-utils": "^3.5.7", "xgplayer": "^2.31.6"}, "devDependencies": {"@antfu/eslint-config": "^2.16.3", "@arco-design/web-vue": "^2.57.0", "@intlify/unplugin-vue-i18n": "^6.0.8", "@types/crypto-js": "^4.2.2", "@types/lodash-es": "^4.17.12", "@types/node": "^20.2.5", "@types/query-string": "^6.3.0", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/tsconfig": "^0.1.3", "boxen": "^7.1.1", "eslint": "^9.0.0", "less": "^4.1.3", "less-loader": "^11.0.0", "picocolors": "^1.0.0", "sass": "^1.62.1", "sass-loader": "^13.2.2", "typescript": "~5.0.4", "unplugin-auto-import": "^0.16.4", "unplugin-vue-components": "^0.25.1", "vite": "^5.1.5", "vite-plugin-mock": "^2.9.8", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "2.0.19"}, "pnpm": {"onlyBuiltDependencies": ["@vue-office/docx", "@vue-office/excel", "@vue-office/pdf", "core-js", "es5-ext", "esbuild", "vue-demi", "vue-echarts"]}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}}