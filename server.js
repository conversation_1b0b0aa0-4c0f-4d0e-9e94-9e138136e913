import fs from 'node:fs'
import path from 'node:path'
import { fileURLToPath } from 'node:url'
import express from 'express'
import compression from 'compression'
import sirv from 'sirv'

const __dirname = path.dirname(fileURLToPath(import.meta.url))
const isProduction = process.env.NODE_ENV === 'production'
const port = process.env.PORT || 5174
const base = process.env.BASE || '/'

// 创建 Express 应用
const app = express()

// 添加 Vite 或相应的生产中间件
let vite
if (!isProduction) {
  const { createServer } = await import('vite')
  vite = await createServer({
    server: { middlewareMode: true },
    appType: 'custom',
    base
  })
  app.use(vite.middlewares)
} else {
  app.use(compression())
  app.use(base, sirv('./dist/client', { extensions: [] }))
}

// 处理所有路由 - 使用更安全的路由模式
app.get('/*', async (req, res) => {
  try {
    const url = req.originalUrl.replace(base, '')

    let template
    let render
    if (!isProduction) {
      // 开发模式：从 Vite 获取模板和渲染函数
      template = fs.readFileSync(path.resolve(__dirname, 'index.html'), 'utf-8')
      template = await vite.transformIndexHtml(url, template)
      render = (await vite.ssrLoadModule('/src/entry-server.ts')).render
    } else {
      // 生产模式：使用预构建的文件
      template = fs.readFileSync(path.resolve(__dirname, 'dist/client/index.html'), 'utf-8')
      render = (await import('./dist/server/entry-server.js')).render
    }

    // 渲染应用
    const rendered = await render(url, {})
    const html = template
      .replace(`<!--preload-links-->`, rendered.preloadLinks || '')
      .replace(`<!--app-html-->`, rendered.html || '')

    res.status(200).set({ 'Content-Type': 'text/html' }).send(html)
  } catch (e) {
    console.error('SSR Error:', e)
    if (!isProduction) {
      vite?.ssrFixStacktrace(e)
    }
    res.status(500).end(e.stack)
  }
})

// 启动服务器
app.listen(port, () => {
  console.log(`🚀 Server started at http://localhost:${port}`)
})
