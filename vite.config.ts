import { URL, fileURLToPath } from 'node:url'
import { defineConfig, loadEnv } from 'vite'
import createVitePlugins from './config/plugins'

export default defineConfig(({ command, mode, isSsrBuild }) => {
  const env = loadEnv(mode, process.cwd()) as ImportMetaEnv

  return {
    // 开发或生产环境服务的公共基础路径
    base: env.VITE_BASE,
    // 路径别名
    resolve: {
      alias: {
        '~': fileURLToPath(new URL('./', import.meta.url)),
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
    // 引入sass全局样式变量
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/styles/var.scss" as *;`,
          api: 'modern-compiler',
        },
      },
    },
    // 添加需要vite优化的依赖
    optimizeDeps: {
      include: ['vue-draggable-plus'],
    },
    server: {
      // 服务启动时是否自动打开浏览器
      open: true,
      // 本地跨域代理 -> 代理到服务器的接口地址
      proxy: {
        [env.VITE_API_PREFIX]: {
          target: env.VITE_API_BASE_URL, // 后台服务器地址
          changeOrigin: true, // 是否允许不同源
          secure: true, // 支持https
          headers: {
            'Referer': 'https://admin.continew.top/',
            'Origin': 'https://admin.continew.top',
          },
          rewrite: (path) => path.replace(new RegExp(`^${env.VITE_API_PREFIX}`), ''),
        }
      },
    },
    plugins: createVitePlugins(env, command === 'build'),
    // SSR 配置
    ssr: {
      noExternal: [
        '@arco-design/web-vue',
        'scroll-into-view-if-needed',
        'compute-scroll-into-view',
        'xe-utils',
      ],
      external: [
        'aieditor',
      ],
    },

    // 构建
    build: {
      chunkSizeWarningLimit: 2000, // 消除打包大小超过500kb警告
      outDir: isSsrBuild ? 'dist/server' : 'dist/client', // SSR 构建输出到不同目录
      minify: isSsrBuild ? false : 'terser', // SSR 构建不压缩
      ssr: isSsrBuild ? 'src/entry-server.ts' : undefined, // SSR 入口
      terserOptions: !isSsrBuild ? {
        compress: {
          keep_infinity: true, // 防止 Infinity 被压缩成 1/0，这可能会导致 Chrome 上的性能问题
          drop_console: true, // 生产环境去除 console
          drop_debugger: true, // 生产环境去除 debugger
        },
        format: {
          comments: false, // 删除注释
        },
      } : undefined,
      // 静态资源打包到dist下的不同目录
      rollupOptions: {
        input: isSsrBuild ? 'src/entry-server.ts' : 'src/entry-client.ts',
        output: isSsrBuild ? {
          format: 'es',
          entryFileNames: '[name].js',
        } : {
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
          assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
        },
      },
    },
    // 以 envPrefix 开头的环境变量会通过 import.meta.env 暴露在你的客户端源码中。
    envPrefix: ['VITE', 'FILE'],
  }
})
