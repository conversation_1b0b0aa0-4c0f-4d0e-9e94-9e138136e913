name: Deploy

on:
  # 推送时执行
  push:
    branches: [dev]
  # 可手动执行
  workflow_dispatch:

jobs:
  deploy-web:
    runs-on: ubuntu-latest
    steps:
      # 1、检出源码
      - name: Checkout
        uses: actions/checkout@master
      # 2、安装 PNPM
      - name: Setup PNPM
        uses: pnpm/action-setup@v2
        with: { version: 9 }
      # 3、安装 Node 环境
      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: pnpm
          cache-dependency-path: ./pnpm-lock.yaml
      # 4、安装依赖
      - name: Install Dependencies
        run: pnpm i --frozen-lockfile
      # 5、打包
      - name: Build
        run: pnpm build
      # 6、拷贝到服务器
      - name: Co<PERSON>
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.SERVER_HOST }}
          port: ${{ secrets.SERVER_PORT }}
          username: ${{ secrets.SERVER_USERNAME }}
          password: ${{ secrets.SERVER_PASSWORD }}
          source: ./dist/*
          target: /tmp/html
          strip_components: 1
      # 7、重启 Nginx
      - name: <PERSON><PERSON>
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SERVER_HOST }}
          port: ${{ secrets.SERVER_PORT }}
          username: ${{ secrets.SERVER_USERNAME }}
          password: ${{ secrets.SERVER_PASSWORD }}
          script: |
            rm -rf ${{ secrets.SERVER_PATH }}/*
            mv /tmp/html/* ${{ secrets.SERVER_PATH }}
            chmod -R 777 ${{ secrets.SERVER_PATH }}
