import { createI18n } from 'vue-i18n'
import en from './en-US.js'
import cn from './zh-CN.js'

export const LOCALE_OPTIONS = [
  { label: '中文', value: 'zh-CN' },
  { label: 'English', value: 'en-US' },
]

// 安全地获取本地存储的语言设置
function getStoredLocale(): string {
  // SSR 环境下 localStorage 不可用
  if (typeof window === 'undefined') {
    return 'zh-CN'
  }

  try {
    return localStorage.getItem('continew-locale') || 'zh-CN'
  } catch {
    return 'zh-CN'
  }
}

const defaultLocale = getStoredLocale()

// 只在客户端打印日志
if (typeof window !== 'undefined') {
  console.log('i18n 初始化，默认语言:', defaultLocale)
}

const i18n = createI18n({
  locale: defaultLocale,
  fallbackLocale: 'en-US',
  legacy: false,
  globalInjection: true,
  messages: {
    'en-US': en,
    'zh-CN': cn,
  },
})

export default i18n
