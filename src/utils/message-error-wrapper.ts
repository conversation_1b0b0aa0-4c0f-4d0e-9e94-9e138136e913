import { Message, type MessageReturn } from '@arco-design/web-vue'

let messageInstance: MessageReturn | null
const messageErrorWrapper = (options: any) => {
  // 在 SSR 环境下跳过消息显示
  if (typeof window === 'undefined') {
    console.warn('SSR: Message error:', options)
    return
  }

  if (messageInstance) {
    messageInstance.close()
  }
  messageInstance = Message.error(options)
}

export default messageErrorWrapper
