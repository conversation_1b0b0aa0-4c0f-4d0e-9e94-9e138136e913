/**
 * 树形数据处理工具函数
 * 替代 xe-utils 的树形数据处理功能，支持 SSR
 */

/**
 * 递归遍历树形数据并应用回调函数
 * @param tree 树形数据数组
 * @param callback 回调函数
 * @returns 处理后的树形数据
 */
export const mapTree = <T>(tree: T[], callback: (item: T) => T): T[] => {
  return tree.map(item => {
    const newItem = callback(item)
    if ((newItem as any).children && Array.isArray((newItem as any).children)) {
      (newItem as any).children = mapTree((newItem as any).children, callback)
    }
    return newItem
  })
}

/**
 * 将树形数据转换为扁平数组
 * @param tree 树形数据数组
 * @returns 扁平化的数组
 */
export const toTreeArray = <T>(tree: T[]): T[] => {
  const result: T[] = []
  const traverse = (nodes: T[]) => {
    nodes.forEach(node => {
      result.push(node)
      if ((node as any).children && Array.isArray((node as any).children)) {
        traverse((node as any).children)
      }
    })
  }
  traverse(tree)
  return result
}

/**
 * 根据条件过滤树形数据
 * @param tree 树形数据数组
 * @param predicate 过滤条件函数
 * @returns 过滤后的树形数据
 */
export const filterTree = <T>(tree: T[], predicate: (item: T) => boolean): T[] => {
  return tree.filter(item => {
    if ((item as any).children && Array.isArray((item as any).children)) {
      (item as any).children = filterTree((item as any).children, predicate)
    }
    return predicate(item)
  })
}

/**
 * 在树形数据中查找节点
 * @param tree 树形数据数组
 * @param predicate 查找条件函数
 * @returns 找到的节点或 undefined
 */
export const findTreeNode = <T>(tree: T[], predicate: (item: T) => boolean): T | undefined => {
  for (const item of tree) {
    if (predicate(item)) {
      return item
    }
    if ((item as any).children && Array.isArray((item as any).children)) {
      const found = findTreeNode((item as any).children, predicate)
      if (found) {
        return found
      }
    }
  }
  return undefined
}

/**
 * 获取树形数据的所有叶子节点
 * @param tree 树形数据数组
 * @returns 叶子节点数组
 */
export const getTreeLeaves = <T>(tree: T[]): T[] => {
  const leaves: T[] = []
  const traverse = (nodes: T[]) => {
    nodes.forEach(node => {
      if (!(node as any).children || (node as any).children.length === 0) {
        leaves.push(node)
      } else {
        traverse((node as any).children)
      }
    })
  }
  traverse(tree)
  return leaves
}

/**
 * 按分组条件对数组进行分组
 * @param array 要分组的数组
 * @param keyFn 分组键函数
 * @returns 分组后的对象
 */
export const groupBy = <T, K extends string | number>(
  array: T[], 
  keyFn: (item: T) => K
): Record<K, T[]> => {
  return array.reduce((groups, item) => {
    const key = keyFn(item)
    if (!groups[key]) {
      groups[key] = []
    }
    groups[key].push(item)
    return groups
  }, {} as Record<K, T[]>)
}

/**
 * 生成指定范围内的随机数
 * @param min 最小值
 * @param max 最大值
 * @returns 随机数
 */
export const random = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

/**
 * 浏览器环境检测
 * @returns 浏览器信息对象
 */
export const browse = () => ({
  isMobile: typeof window !== 'undefined' &&
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
})

/**
 * 在树形数据中查找节点
 * @param tree 树形数据数组
 * @param predicate 查找条件函数
 * @returns 查找结果对象，包含找到的节点和相关信息
 */
export const findTree = <T>(tree: T[], predicate: (item: T) => boolean): { item: T | null, index: number, items: T[] } => {
  for (let i = 0; i < tree.length; i++) {
    const item = tree[i]
    if (predicate(item)) {
      return { item, index: i, items: tree }
    }
    if ((item as any).children && Array.isArray((item as any).children)) {
      const found = findTree((item as any).children, predicate)
      if (found.item) {
        return found
      }
    }
  }
  return { item: null, index: -1, items: tree }
}

/**
 * 在树形数据中搜索节点
 * @param tree 树形数据数组
 * @param predicate 搜索条件函数
 * @returns 搜索结果数组
 */
export const searchTree = <T>(tree: T[], predicate: (item: T) => boolean): T[] => {
  const result: T[] = []
  const traverse = (nodes: T[]) => {
    nodes.forEach(node => {
      if (predicate(node)) {
        result.push(node)
      }
      if ((node as any).children && Array.isArray((node as any).children)) {
        traverse((node as any).children)
      }
    })
  }
  traverse(tree)
  return result
}

/**
 * 遍历树形数据的每个节点
 * @param tree 树形数据数组
 * @param callback 回调函数
 */
export const eachTree = <T>(tree: T[], callback: (item: T) => void): void => {
  const traverse = (nodes: T[]) => {
    nodes.forEach(node => {
      callback(node)
      if ((node as any).children && Array.isArray((node as any).children)) {
        traverse((node as any).children)
      }
    })
  }
  traverse(tree)
}
