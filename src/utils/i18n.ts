import i18n from '@/locale'

/**
 * 获取翻译文本
 * @param key 翻译键
 * @param params 参数
 * @returns 翻译后的文本
 */
export function $t(key: string, params?: Record<string, any>): string {
  return i18n.global.t(key, params)
}

/**
 * 检查翻译键是否存在
 * @param key 翻译键
 * @returns 是否存在
 */
export function hasTranslation(key: string): boolean {
  return i18n.global.te(key)
}

/**
 * 获取当前语言
 * @returns 当前语言代码
 */
export function getCurrentLocale(): string {
  return i18n.global.locale.value
}

/**
 * 设置语言
 * @param locale 语言代码
 */
export function setLocale(locale: string): void {
  i18n.global.locale.value = locale
  localStorage.setItem('continew-locale', locale)
  document.documentElement.lang = locale
}

/**
 * 获取所有可用的语言选项
 * @returns 语言选项数组
 */
export function getAvailableLocales() {
  return [
    { label: '中文', value: 'zh-CN' },
    { label: 'English', value: 'en-US' },
  ]
}
