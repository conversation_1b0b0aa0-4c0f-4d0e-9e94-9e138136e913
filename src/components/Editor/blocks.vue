<template>
  <div class="news-block">
    <input v-model="newItem" placeholder="输入新闻标题" />
    <button @click="addItem">添加</button>
    <ul>
      <li v-for="(item, index) in data.items" :key="index">
        {{ item.title }}
        <button @click="removeItem(index)">×</button>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'NewsBlock',
  props: ['data', 'api'],
  data() {
    return {
      newItem: '',
    }
  },
  methods: {
    addItem() {
      if (!this.newItem) return
      this.data.items.push({ title: this.newItem })
      this.newItem = ''
      this.api.blocks.render()
    },
    removeItem(index) {
      this.data.items.splice(index, 1)
      this.api.blocks.render()
    },
  },
}
</script>

<style scoped>
.news-block {
  padding: 10px;
  border: 1px solid #ddd;
  margin: 10px 0;
}
</style>
