<template>
  <div class="locale-switch">
    <a-button type="text" size="small" @click="toggleLocale">
      <template #icon>
        <icon-language />
      </template>
      {{ currentLabel }}
    </a-button>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { IconLanguage } from '@arco-design/web-vue/es/icon'
import useLocale from '@/hooks/useLocale'

const { currentLocale, changeLocale, getLocaleLabel } = useLocale()

const currentLabel = computed(() => getLocaleLabel(currentLocale.value))

const toggleLocale = () => {
  const newLocale = currentLocale.value === 'zh-CN' ? 'en-US' : 'zh-CN'
  console.log('切换语言从', currentLocale.value, '到', newLocale)
  changeLocale(newLocale)
}
</script>

<style scoped>
.locale-switch {
  display: inline-block;
}
</style>
