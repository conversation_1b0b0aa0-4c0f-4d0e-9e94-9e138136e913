<template>
  <a-dropdown trigger="hover" @select="handleSelect">
    <a-button type="text" size="small">
      <template #icon>
        <icon-language />
      </template>
      {{ getLocaleLabel(currentLocale) }}
    </a-button>
    <template #content>
      <a-doption
        v-for="item in localeOptions"
        :key="item.value"
        :value="item.value"
        :class="{ 'arco-dropdown-option-active': item.value === currentLocale }"
      >
        <template #icon>
          <icon-check v-if="item.value === currentLocale" />
        </template>
        {{ item.label }}
      </a-doption>
    </template>
  </a-dropdown>
</template>

<script setup lang="ts">
import { IconLanguage, IconCheck } from '@arco-design/web-vue/es/icon'
import useLocale from '@/hooks/useLocale'

const { currentLocale, changeLocale, getLocaleLabel, localeOptions } = useLocale()

const handleSelect = (value: string) => {
  changeLocale(value)
}
</script>

<style scoped>
.arco-dropdown-option-active {
  background-color: var(--color-primary-light-1);
  color: var(--color-primary-6);
}
</style>
