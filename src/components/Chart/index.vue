<template>
  <div v-if="isClient" :style="{ width, height }">
    <VCharts ref="chart" :option="option" :autoresize="autoResize" :style="{ width, height }" />
  </div>
  <div v-else :style="{ width, height }" class="chart-placeholder">
    <!-- SSR 占位符 -->
    <div class="chart-loading">图表加载中...</div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 动态导入 ECharts 相关模块，避免 SSR 问题
let VCharts: any = null
let registerMap: any = null
let worldMap: any = null
let chinaMap: any = null

const isClient = ref(false)

defineProps({
  option: {
    type: Object,
    default() {
      return {}
    },
  },
  autoResize: {
    type: Boolean,
    default: true,
  },
  width: {
    type: String,
    default: '100%',
  },
  height: {
    type: String,
    default: '100%',
  },
})

const chart = ref(null)

// 在客户端挂载时动态加载 ECharts
onMounted(async () => {
  if (typeof window !== 'undefined') {
    try {
      // 动态导入 ECharts 相关模块
      const [echartsCore, vueEcharts, worldMapData, chinaMapData] = await Promise.all([
        import('echarts/core'),
        import('vue-echarts'),
        import('./world.json'),
        import('./china.json')
      ])

      VCharts = vueEcharts.default
      registerMap = echartsCore.registerMap
      worldMap = worldMapData.default
      chinaMap = chinaMapData.default

      // 注册地图
      registerMap('world', worldMap)
      registerMap('china', chinaMap)

      isClient.value = true
    } catch (error) {
      console.error('Failed to load ECharts:', error)
    }
  }
})

defineExpose({
  chart,
})
</script>

<style scoped lang="less">
.chart-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
}

.chart-loading {
  color: #999;
  font-size: 14px;
}
</style>
