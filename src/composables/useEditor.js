// import EditorJS from '@editorjs/editorjs'
// import Header from '@editorjs/header'
// import NewsBlock from '../components/editor/blocks/NewsBlock.vue'

export const useEditor = () => {
  const editor = ref()

  // 渲染HTML预览
  const renderHtml = (data) => {
    return data.blocks.map((block) => {
      switch (block.type) {
        case 'news':
          return `<div class="news-preview">
            <h3>新闻公告</h3>
            <ul>${block.data.items.map((item) => `<li>${item.title}</li>`).join('')}</ul>
          </div>`
        case 'header':
          return `<h${block.data.level}>${block.data.text}</h${block.data.level}>`
        default:
          return ''
      }
    }).join('')
  }

  return { editor, renderHtml }
}
