import { createSSRApp } from 'vue'
import { renderToString } from 'vue/server-renderer'
import ArcoVue from '@arco-design/web-vue'
import ArcoVueIcon from '@arco-design/web-vue/es/icon'
import App from './App.vue'
import { createRouterInstance } from './router'
import { createPinia } from 'pinia'
import i18n from './locale'

// 导入样式（服务端渲染时需要）
import '@/styles/arco-ui/index.less'
import 'animate.css/animate.min.css'
import '@/styles/css/transition.css'
import '@/styles/index.scss'

export async function render(url: string, manifest?: Record<string, string[]>) {
  const app = createSSRApp(App)
  const router = createRouterInstance()
  const pinia = createPinia()

  // 注册插件
  app.use(router)
  app.use(pinia)
  app.use(i18n)
  app.use(ArcoVue)
  app.use(ArcoVueIcon)

  // 导航到请求的路由
  await router.push(url)
  await router.isReady()

  // 渲染应用
  const html = await renderToString(app)

  // 获取预加载的模块
  const preloadLinks = renderPreloadLinks(router.currentRoute.value, manifest)

  return {
    html,
    preloadLinks,
  }
}

function renderPreloadLinks(route: any, manifest?: Record<string, string[]>) {
  let links = ''
  if (manifest) {
    const files = new Set<string>()
    
    // 添加入口文件
    if (manifest['src/main.ts']) {
      manifest['src/main.ts'].forEach(file => files.add(file))
    }

    // 添加路由相关文件
    const routeFiles = getRouteFiles(route, manifest)
    routeFiles.forEach(file => files.add(file))

    files.forEach(file => {
      if (file.endsWith('.js')) {
        links += `<link rel="modulepreload" crossorigin href="${file}">`
      } else if (file.endsWith('.css')) {
        links += `<link rel="stylesheet" href="${file}">`
      }
    })
  }
  return links
}

function getRouteFiles(route: any, manifest: Record<string, string[]>): string[] {
  const files: string[] = []
  
  // 根据路由匹配的组件获取相关文件
  if (route.matched) {
    route.matched.forEach((matched: any) => {
      if (matched.components) {
        Object.values(matched.components).forEach((component: any) => {
          if (component.__file && manifest[component.__file]) {
            files.push(...manifest[component.__file])
          }
        })
      }
    })
  }
  
  return files
}
