import { createSSRApp } from 'vue'
import { renderToString } from 'vue/server-renderer'
import ArcoVue from '@arco-design/web-vue'
import ArcoVueIcon from '@arco-design/web-vue/es/icon'
import App from './App.vue'
import { createRouterInstance } from './router'
import { createPinia } from 'pinia'
import i18n from './locale'
import { useRouteStore, useUserStore } from '@/stores'
import { getToken } from '@/utils/auth'
import { isHttp } from '@/utils/validate'

// 导入样式（服务端渲染时需要）
import '@/styles/arco-ui/index.less'
import 'animate.css/animate.min.css'
import '@/styles/css/transition.css'
import '@/styles/index.scss'



export async function render(url: string, manifest?: Record<string, string[]>, context?: { token?: string }) {
  const app = createSSRApp(App)
  const router = createRouterInstance()
  const pinia = createPinia()

  // 注册插件
  app.use(router)
  app.use(pinia)
  app.use(i18n)
  app.use(ArcoVue)
  app.use(ArcoVueIcon)

  // 在 SSR 环境下处理动态路由加载
  try {
    // 检查是否有 token
    const hasToken = context?.token || false

    if (hasToken) {
      // 获取 store 实例
      const routeStore = useRouteStore()
      const userStore = useUserStore()

      try {
        // 在 SSR 环境下，我们可以：
        // 1. 从缓存中获取路由数据
        // 2. 使用预定义的路由配置
        // 3. 或者尝试调用 API（如果在服务端可用）

        console.log('SSR: Attempting to load dynamic routes...')

        // 检查是否使用测试 token，如果是则使用模拟数据
        if (hasToken && context.token.startsWith('test-token')) {
          console.log('SSR: Using mock route data for test token')

          // 模拟路由数据 - 使用正确的 Vue Router 结构
          const mockRoutes = [
            {
              path: '/system',
              name: 'System',
              component: () => import('@/layout/index.vue'),
              meta: { title: '系统管理', icon: 'settings' },
              children: [
                {
                  path: 'user', // 子路由使用相对路径
                  name: 'SystemUser',
                  component: () => import('@/views/system/user/index.vue'),
                  meta: { title: '用户管理', icon: 'user' }
                },
                {
                  path: 'role', // 子路由使用相对路径
                  name: 'SystemRole',
                  component: () => import('@/views/system/role/index.vue'),
                  meta: { title: '角色管理', icon: 'user-group' }
                }
              ]
            },
            {
              path: '/monitor',
              name: 'Monitor',
              component: () => import('@/layout/index.vue'),
              meta: { title: '监控管理', icon: 'monitor' },
              children: [
                {
                  path: 'online', // 子路由使用相对路径
                  name: 'MonitorOnline',
                  component: () => import('@/views/monitor/online/index.vue'),
                  meta: { title: '在线用户', icon: 'user-switch' }
                }
              ]
            }
          ]

          // 添加模拟路由到路由器
          let addedCount = 0
          mockRoutes.forEach((route) => {
            router.addRoute(route)
            addedCount++
            console.log(`SSR: Added mock route: ${route.name} (${route.path})`)
          })

          console.log(`SSR: Successfully loaded ${addedCount} mock dynamic routes`)
        } else {
          // 这里我们先尝试生成路由，如果失败则跳过
          const accessRoutes = await routeStore.generateRoutes()

          console.log(`SSR: Generated ${accessRoutes.length} dynamic routes`)

          // 添加动态路由到路由器
          let addedCount = 0
          accessRoutes.forEach((route) => {
            if (!isHttp(route.path)) {
              router.addRoute(route)
              addedCount++
              console.log(`SSR: Added route: ${route.name} (${route.path})`)
            }
          })

          console.log(`SSR: Successfully loaded ${addedCount} dynamic routes`)
        }
      } catch (error) {
        // 如果在 SSR 环境下无法获取动态路由，我们跳过
        // 让客户端在水合时处理动态路由加载
        console.warn('SSR: Failed to load dynamic routes, will be handled on client side:', error)
      }
    } else {
      console.log('SSR: No token found, skipping dynamic route loading')
    }
  } catch (error) {
    console.warn('SSR: Error handling dynamic routes:', error)
  }

  // 导航到请求的路由
  await router.push(url)
  await router.isReady()

  // 渲染应用
  const html = await renderToString(app)

  // 获取预加载的模块
  const preloadLinks = renderPreloadLinks(router.currentRoute.value, manifest)

  return {
    html,
    preloadLinks,
    // 传递 SSR 状态给客户端
    ssrContext: {
      hasToken: !!context?.token,
      routesLoaded: !!context?.token && context.token.startsWith('test-token')
    }
  }
}

function renderPreloadLinks(route: any, manifest?: Record<string, string[]>) {
  let links = ''
  if (manifest) {
    const files = new Set<string>()
    
    // 添加入口文件
    if (manifest['src/main.ts']) {
      manifest['src/main.ts'].forEach(file => files.add(file))
    }

    // 添加路由相关文件
    const routeFiles = getRouteFiles(route, manifest)
    routeFiles.forEach(file => files.add(file))

    files.forEach(file => {
      if (file.endsWith('.js')) {
        links += `<link rel="modulepreload" crossorigin href="${file}">`
      } else if (file.endsWith('.css')) {
        links += `<link rel="stylesheet" href="${file}">`
      }
    })
  }
  return links
}

function getRouteFiles(route: any, manifest: Record<string, string[]>): string[] {
  const files: string[] = []
  
  // 根据路由匹配的组件获取相关文件
  if (route.matched) {
    route.matched.forEach((matched: any) => {
      if (matched.components) {
        Object.values(matched.components).forEach((component: any) => {
          if (component.__file && manifest[component.__file]) {
            files.push(...manifest[component.__file])
          }
        })
      }
    })
  }
  
  return files
}
