import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { LOCALE_OPTIONS } from '@/locale'

export default function useLocale() {
  const i18n = useI18n()
  
  const currentLocale = computed(() => {
    return i18n.locale.value
  })
  
  const changeLocale = (value: string) => {
    i18n.locale.value = value
    localStorage.setItem('continew-locale', value)
    // 同时更新 Arco Design 的语言
    document.documentElement.lang = value
  }
  
  const getLocaleLabel = (value: string) => {
    const option = LOCALE_OPTIONS.find(item => item.value === value)
    return option?.label || value
  }
  
  return {
    currentLocale,
    changeLocale,
    getLocaleLabel,
    localeOptions: LOCALE_OPTIONS,
  }
}
