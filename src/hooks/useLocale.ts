import { computed, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { LOCALE_OPTIONS } from '@/locale'

export default function useLocale() {
  const i18n = useI18n()

  const currentLocale = computed(() => {
    return i18n.locale.value
  })

  const changeLocale = async (value: string) => {
    console.log('useLocale changeLocale 被调用:', value)
    console.log('当前 i18n.locale.value:', i18n.locale.value)

    try {
      // 更新 i18n 语言
      i18n.locale.value = value

      // 保存到本地存储（仅客户端）
      if (typeof window !== 'undefined') {
        localStorage.setItem('continew-locale', value)

        // 更新 HTML 语言属性
        document.documentElement.lang = value
      }

      // 等待下一个 tick 确保更新完成
      await nextTick()

      console.log('切换完成，新的 i18n.locale.value:', i18n.locale.value)

      // 强制刷新页面以确保所有组件都更新（仅客户端）
      if (typeof window !== 'undefined') {
        setTimeout(() => {
          window.location.reload()
        }, 100)
      }

    } catch (error) {
      console.error('语言切换失败:', error)
    }
  }

  const getLocaleLabel = (value: string) => {
    const option = LOCALE_OPTIONS.find(item => item.value === value)
    return option?.label || value
  }

  return {
    currentLocale,
    changeLocale,
    getLocaleLabel,
    localeOptions: LOCALE_OPTIONS,
  }
}
