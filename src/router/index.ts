import { createRouter, createWebHistory, createMemoryHistory } from 'vue-router'
import { useRouteStore } from '@/stores'
import { constantRoutes, systemRoutes } from '@/router/route'
import { setupRouterGuard } from '@/router/guard'

// 创建路由实例的工厂函数，支持 SSR
export function createRouterInstance() {
  const router = createRouter({
    history: import.meta.env.SSR
      ? createMemoryHistory(import.meta.env.BASE_URL)
      : createWebHistory(import.meta.env.BASE_URL),
    routes: [...constantRoutes, ...systemRoutes],
    scrollBehavior: () => ({ left: 0, top: 0 }),
  })

  // 只在客户端设置路由守卫
  if (!import.meta.env.SSR) {
    setupRouterGuard(router)
  }

  return router
}

// 默认导出（兼容现有代码）
const router = createRouterInstance()

// 导出创建函数供 SSR 使用
export { createRouterInstance as createRouter }

/**
 * @description 重置路由
 * @description 注意：所有动态路由路由必须带有 name 属性，否则可能会不能完全重置干净
 */
export function resetRouter() {
  try {
    const routeStore = useRouteStore()
    routeStore.asyncRoutes.forEach((route) => {
      const { name } = route
      if (name) {
        router.hasRoute(name) && router.removeRoute(name)
      }
    })
  } catch (error) {
    // 强制刷新浏览器也行，只是交互体验不是很好
    window.location.reload()
  }
}

export default router
