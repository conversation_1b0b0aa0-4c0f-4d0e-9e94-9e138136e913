<template>
  <div class="i18n-demo">
    <a-card title="国际化 (i18n) 功能演示">
      <a-space direction="vertical" size="large" style="width: 100%">
        <!-- 语言切换 -->
        <a-card size="small" title="语言切换">
          <a-space>
            <span>当前语言：</span>
            <LocaleSwitch />
            <span>{{ getLocaleLabel(currentLocale) }}</span>
          </a-space>
        </a-card>

        <!-- 基础翻译 -->
        <a-card size="small" title="基础翻译示例">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="确认">{{ t('common.confirm') }}</a-descriptions-item>
            <a-descriptions-item label="取消">{{ t('common.cancel') }}</a-descriptions-item>
            <a-descriptions-item label="保存">{{ t('common.save') }}</a-descriptions-item>
            <a-descriptions-item label="删除">{{ t('common.delete') }}</a-descriptions-item>
            <a-descriptions-item label="编辑">{{ t('common.edit') }}</a-descriptions-item>
            <a-descriptions-item label="新增">{{ t('common.add') }}</a-descriptions-item>
            <a-descriptions-item label="搜索">{{ t('common.search') }}</a-descriptions-item>
            <a-descriptions-item label="重置">{{ t('common.reset') }}</a-descriptions-item>
          </a-descriptions>
        </a-card>

        <!-- 菜单翻译 -->
        <a-card size="small" title="菜单翻译示例">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="仪表盘">{{ t('menu.dashboard') }}</a-descriptions-item>
            <a-descriptions-item label="系统管理">{{ t('menu.system') }}</a-descriptions-item>
            <a-descriptions-item label="用户管理">{{ t('menu.user') }}</a-descriptions-item>
            <a-descriptions-item label="角色管理">{{ t('menu.role') }}</a-descriptions-item>
            <a-descriptions-item label="菜单管理">{{ t('menu.menu') }}</a-descriptions-item>
            <a-descriptions-item label="部门管理">{{ t('menu.dept') }}</a-descriptions-item>
          </a-descriptions>
        </a-card>

        <!-- 登录页面翻译 -->
        <a-card size="small" title="登录页面翻译示例">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="用户登录">{{ t('login.title') }}</a-descriptions-item>
            <a-descriptions-item label="用户名">{{ t('login.username') }}</a-descriptions-item>
            <a-descriptions-item label="密码">{{ t('login.password') }}</a-descriptions-item>
            <a-descriptions-item label="手机号">{{ t('login.phone') }}</a-descriptions-item>
            <a-descriptions-item label="邮箱">{{ t('login.email') }}</a-descriptions-item>
            <a-descriptions-item label="验证码">{{ t('login.captcha') }}</a-descriptions-item>
          </a-descriptions>
        </a-card>

        <!-- 消息提示翻译 -->
        <a-card size="small" title="消息提示翻译示例">
          <a-space>
            <a-button type="primary" @click="showSuccess">{{ t('message.success') }}</a-button>
            <a-button status="danger" @click="showError">{{ t('message.error') }}</a-button>
            <a-button status="warning" @click="showWarning">{{ t('message.warning') }}</a-button>
            <a-button @click="showInfo">{{ t('message.info') }}</a-button>
          </a-space>
        </a-card>

        <!-- 表格翻译 -->
        <a-card size="small" title="表格翻译示例">
          <a-table :columns="columns" :data="tableData" :pagination="false">
            <template #action="{ record }">
              <a-space>
                <a-button size="mini" type="text">{{ t('common.edit') }}</a-button>
                <a-button size="mini" type="text" status="danger">{{ t('common.delete') }}</a-button>
              </a-space>
            </template>
          </a-table>
        </a-card>

        <!-- 参数化翻译 -->
        <a-card size="small" title="参数化翻译示例">
          <a-descriptions :column="1" bordered>
            <a-descriptions-item label="总数显示">{{ t('common.total', { total: 100 }) }}</a-descriptions-item>
            <a-descriptions-item label="分页显示">{{ t('pagination.total', { total: 256 }) }}</a-descriptions-item>
            <a-descriptions-item label="每页条数">{{ t('pagination.pageSize', { size: 20 }) }}</a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-space>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import { useI18n } from 'vue-i18n'
import LocaleSwitch from '@/components/LocaleSwitch/index.vue'
import useLocale from '@/hooks/useLocale'

defineOptions({ name: 'I18nDemo' })

const { t } = useI18n()
const { currentLocale, getLocaleLabel } = useLocale()

// 表格列定义
const columns = computed(() => [
  {
    title: t('table.index'),
    dataIndex: 'index',
    width: 80,
  },
  {
    title: t('login.username'),
    dataIndex: 'username',
  },
  {
    title: t('login.email'),
    dataIndex: 'email',
  },
  {
    title: t('common.status'),
    dataIndex: 'status',
  },
  {
    title: t('common.createTime'),
    dataIndex: 'createTime',
  },
  {
    title: t('common.operation'),
    slotName: 'action',
    width: 150,
  },
])

// 表格数据
const tableData = [
  {
    index: 1,
    username: 'admin',
    email: '<EMAIL>',
    status: '启用',
    createTime: '2024-01-01 10:00:00',
  },
  {
    index: 2,
    username: 'user',
    email: '<EMAIL>',
    status: '禁用',
    createTime: '2024-01-02 11:00:00',
  },
]

// 消息提示方法
const showSuccess = () => {
  Message.success(t('message.success'))
}

const showError = () => {
  Message.error(t('message.error'))
}

const showWarning = () => {
  Message.warning(t('message.warning'))
}

const showInfo = () => {
  Message.info(t('message.info'))
}
</script>

<style scoped lang="scss">
.i18n-demo {
  padding: 20px;
}
</style>
