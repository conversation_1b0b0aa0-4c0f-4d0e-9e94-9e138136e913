<template>
  <div class="demo-page">
    <ArticleList
      v-model="articleListData"
      @handle-copy="handleCopy"
      @handle-delete="handleDelete"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const articleListData = ref()

const handleCopy = () => {
  console.log('handleCopy')
}

const handleDelete = () => {
  console.log('handleDelete')
}
</script>

<style scoped lang="scss">
.demo-page {
  background: #fff;
  height: 100%;
  overflow: auto;
}
</style>
