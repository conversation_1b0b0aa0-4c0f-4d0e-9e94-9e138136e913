<template>
  <div class="demo">
    <main class="main">
      <section class="component-section">
        <h2>HeroSlide 轮播组件</h2>
        <div class="component-demo">
          <HeroSlide />
        </div>
      </section>

      <section class="component-section">
        <h2>ArticleList 文章列表组件</h2>
        <div class="component-demo">
          <ArticleList
            v-model="articleValue"
            size="medium"
            :disabled="false"
            @change="(value: string) => console.log('ArticleList changed:', value)"
          >
            <template #header="{ title }">
              <h3>{{ title }}</h3>
            </template>
            <template #default="{ value }">
              <p>当前值: {{ value }}</p>
            </template>
          </ArticleList>
        </div>
      </section>
    </main>
  </div>
</template>

<script lang="ts" setup>
import { ArticleList, HeroSlide } from 'officialblock'
import 'officialblock/style.css'

const articleValue = ref('Hello World')
</script>

<style scoped lang="scss">
.demo {
  height: 100%;

  .main {
    display: flex;
    flex-direction: column;
    gap: 30px;
  }

  .component-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 24px;
    border: 1px solid #e9ecef;
  }

  .component-section h2 {
    margin: 0 0 20px 0;
    color: #495057;
    font-size: 1.5rem;
    font-weight: 500;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 10px;
  }

  .component-demo {
    background: white;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}
</style>
