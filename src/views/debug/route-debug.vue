<template>
  <div class="route-debug">
    <a-card title="路由调试工具">
      <a-space direction="vertical" size="large" style="width: 100%">
        <!-- 当前状态 -->
        <a-descriptions title="当前状态" :column="2">
          <a-descriptions-item label="是否有Token">
            <a-tag :color="hasToken ? 'green' : 'red'">
              {{ hasToken ? '是' : '否' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="Token值">
            {{ token || '无' }}
          </a-descriptions-item>
          <a-descriptions-item label="路由标志">
            <a-tag :color="routeFlag ? 'green' : 'red'">
              {{ routeFlag ? '已加载' : '未加载' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="动态路由数量">
            {{ asyncRoutes.length }}
          </a-descriptions-item>
        </a-descriptions>

        <!-- 操作按钮 -->
        <a-space>
          <a-button type="primary" @click="setTestToken">
            设置测试Token
          </a-button>
          <a-button @click="clearTestToken">
            清除Token
          </a-button>
          <a-button @click="loadRoutes" :loading="loading">
            手动加载路由
          </a-button>
          <a-button @click="refreshPage">
            刷新页面
          </a-button>
        </a-space>

        <!-- 路由列表 -->
        <a-card title="当前所有路由" size="small">
          <a-tree 
            :data="routeTree" 
            :show-line="true"
            :default-expand-all="true"
          />
        </a-card>

        <!-- 动态路由列表 -->
        <a-card title="动态路由" size="small">
          <a-tree 
            :data="asyncRouteTree" 
            :show-line="true"
            :default-expand-all="true"
          />
        </a-card>

        <!-- 日志 -->
        <a-card title="操作日志" size="small">
          <div class="log-container">
            <div v-for="(log, index) in logs" :key="index" class="log-item">
              <span class="log-time">{{ log.time }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </a-card>
      </a-space>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useRouteStore, useUserStore } from '@/stores'
import { getToken, setToken, clearToken } from '@/utils/auth'
import { resetHasRouteFlag } from '@/router/guard'
import { isHttp } from '@/utils/validate'

defineOptions({ name: 'RouteDebug' })

const router = useRouter()
const routeStore = useRouteStore()
const userStore = useUserStore()

const loading = ref(false)
const logs = ref<Array<{ time: string, message: string }>>([])

// 计算属性
const hasToken = computed(() => !!getToken())
const token = computed(() => getToken())
const asyncRoutes = computed(() => routeStore.asyncRoutes)
const routeFlag = computed(() => {
  // 这里我们通过检查是否有动态路由来判断
  return asyncRoutes.value.length > 0
})

// 路由树数据
const routeTree = computed(() => {
  return router.getRoutes().map(route => ({
    title: `${route.name || route.path} (${route.path})`,
    key: route.path,
    children: route.children?.map(child => ({
      title: `${child.name || child.path} (${child.path})`,
      key: child.path
    }))
  }))
})

const asyncRouteTree = computed(() => {
  return asyncRoutes.value.map(route => ({
    title: `${route.name || route.path} (${route.path})`,
    key: route.path,
    children: route.children?.map(child => ({
      title: `${child.name || child.path} (${child.path})`,
      key: child.path
    }))
  }))
})

// 添加日志
const addLog = (message: string) => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message
  })
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
}

// 设置测试Token
const setTestToken = () => {
  const testToken = 'test-token-' + Date.now()
  setToken(testToken)
  addLog(`设置测试Token: ${testToken}`)
  addLog('请刷新页面以测试 SSR 动态路由加载')
}

// 清除Token
const clearTestToken = () => {
  clearToken()
  resetHasRouteFlag()
  addLog('已清除Token和路由标志')
}

// 手动加载路由
const loadRoutes = async () => {
  loading.value = true
  try {
    addLog('开始加载动态路由...')
    
    const accessRoutes = await routeStore.generateRoutes()
    addLog(`获取到 ${accessRoutes.length} 个动态路由`)
    
    accessRoutes.forEach((route) => {
      if (!isHttp(route.path)) {
        router.addRoute(route)
        addLog(`添加路由: ${route.name} (${route.path})`)
      }
    })
    
    addLog('动态路由加载完成')
  } catch (error: any) {
    addLog(`路由加载失败: ${error.message}`)
  } finally {
    loading.value = false
  }
}

// 刷新页面
const refreshPage = () => {
  window.location.reload()
}

onMounted(() => {
  addLog('路由调试工具已加载')
  addLog(`当前Token状态: ${hasToken.value ? '有' : '无'}`)
  addLog(`当前动态路由数量: ${asyncRoutes.value.length}`)
})
</script>

<style scoped lang="scss">
.route-debug {
  padding: 20px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
}

.log-item {
  margin-bottom: 5px;
  font-family: monospace;
  font-size: 12px;
}

.log-time {
  color: #666;
  margin-right: 10px;
}

.log-message {
  color: #333;
}
</style>
