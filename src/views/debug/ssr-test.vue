<template>
  <div class="ssr-test">
    <a-card title="SSR 动态路由测试">
      <a-space direction="vertical" size="large" style="width: 100%">
        <!-- 当前状态 -->
        <a-descriptions title="当前状态" :column="2">
          <a-descriptions-item label="SSR 状态">
            <a-tag :color="ssrState ? 'green' : 'red'">
              {{ ssrState ? '检测到 SSR 状态' : '无 SSR 状态' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="路由已加载">
            <a-tag :color="ssrState?.routesLoaded ? 'green' : 'red'">
              {{ ssrState?.routesLoaded ? '是' : '否' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="有Token">
            <a-tag :color="ssrState?.hasToken ? 'green' : 'red'">
              {{ ssrState?.hasToken ? '是' : '否' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="客户端Token">
            <a-tag :color="hasClientToken ? 'green' : 'red'">
              {{ hasClientToken ? '是' : '否' }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>

        <!-- 测试链接 -->
        <a-card title="测试链接" size="small">
          <a-space wrap>
            <a-button type="primary" @click="testSystemRoute">
              测试系统管理路由
            </a-button>
            <a-button type="primary" @click="testMonitorRoute">
              测试监控管理路由
            </a-button>
            <a-button @click="setTokenAndReload">
              设置Token并重新加载
            </a-button>
            <a-button @click="clearTokenAndReload">
              清除Token并重新加载
            </a-button>
          </a-space>
        </a-card>

        <!-- 路由信息 -->
        <a-card title="当前路由信息" size="small">
          <a-descriptions :column="1">
            <a-descriptions-item label="当前路径">
              {{ $route.path }}
            </a-descriptions-item>
            <a-descriptions-item label="当前路由名">
              {{ $route.name }}
            </a-descriptions-item>
            <a-descriptions-item label="路由参数">
              {{ JSON.stringify($route.params) }}
            </a-descriptions-item>
            <a-descriptions-item label="查询参数">
              {{ JSON.stringify($route.query) }}
            </a-descriptions-item>
          </a-descriptions>
        </a-card>

        <!-- 所有路由 -->
        <a-card title="所有注册的路由" size="small">
          <div class="route-list">
            <div v-for="route in allRoutes" :key="route.path" class="route-item">
              <a-tag>{{ route.name || route.path }}</a-tag>
              <span class="route-path">{{ route.path }}</span>
            </div>
          </div>
        </a-card>

        <!-- 测试结果 -->
        <a-card title="测试结果" size="small">
          <div class="test-results">
            <div v-for="(result, index) in testResults" :key="index" class="test-result">
              <a-tag :color="result.success ? 'green' : 'red'">
                {{ result.success ? '成功' : '失败' }}
              </a-tag>
              <span>{{ result.message }}</span>
            </div>
          </div>
        </a-card>
      </a-space>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getToken, setToken, clearToken } from '@/utils/auth'

defineOptions({ name: 'SSRTest' })

const router = useRouter()
const testResults = ref<Array<{ success: boolean, message: string }>>([])

// SSR 状态
const ssrState = ref<any>(null)

// 计算属性
const hasClientToken = computed(() => !!getToken())
const allRoutes = computed(() => {
  return router.getRoutes().map(route => ({
    name: route.name,
    path: route.path
  }))
})

// 添加测试结果
const addTestResult = (success: boolean, message: string) => {
  testResults.value.unshift({ success, message })
  if (testResults.value.length > 10) {
    testResults.value = testResults.value.slice(0, 10)
  }
}

// 测试系统管理路由
const testSystemRoute = async () => {
  try {
    await router.push('/system')
    addTestResult(true, '成功导航到系统管理页面')
  } catch (error: any) {
    addTestResult(false, `导航失败: ${error.message}`)
  }
}

// 测试监控管理路由
const testMonitorRoute = async () => {
  try {
    await router.push('/monitor')
    addTestResult(true, '成功导航到监控管理页面')
  } catch (error: any) {
    addTestResult(false, `导航失败: ${error.message}`)
  }
}

// 设置Token并重新加载
const setTokenAndReload = () => {
  const testToken = 'test-token-' + Date.now()
  setToken(testToken)
  addTestResult(true, `设置Token: ${testToken}`)
  setTimeout(() => {
    window.location.reload()
  }, 1000)
}

// 清除Token并重新加载
const clearTokenAndReload = () => {
  clearToken()
  addTestResult(true, '已清除Token')
  setTimeout(() => {
    window.location.reload()
  }, 1000)
}

onMounted(() => {
  // 读取 SSR 状态
  ssrState.value = (window as any).__SSR_STATE__ || null
  
  if (ssrState.value) {
    addTestResult(true, `检测到 SSR 状态: ${JSON.stringify(ssrState.value)}`)
  } else {
    addTestResult(false, '未检测到 SSR 状态')
  }
  
  addTestResult(true, `当前注册的路由数量: ${allRoutes.value.length}`)
})
</script>

<style scoped lang="scss">
.ssr-test {
  padding: 20px;
}

.route-list {
  max-height: 200px;
  overflow-y: auto;
}

.route-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  
  .route-path {
    margin-left: 10px;
    color: #666;
    font-family: monospace;
  }
}

.test-results {
  max-height: 200px;
  overflow-y: auto;
}

.test-result {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  
  span {
    margin-left: 10px;
  }
}
</style>
