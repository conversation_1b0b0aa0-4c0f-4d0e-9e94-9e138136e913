<template>
  <div class="token-test">
    <a-card title="Token 测试工具">
      <a-space direction="vertical" size="large" style="width: 100%">
        <!-- 当前状态 -->
        <a-descriptions title="当前状态" :column="2">
          <a-descriptions-item label="当前Token">
            <a-tag :color="hasToken ? 'green' : 'red'">
              {{ token || '无Token' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="路由状态">
            <a-tag :color="routeFlag ? 'green' : 'red'">
              {{ routeFlag ? '已加载' : '未加载' }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>

        <!-- 操作按钮 -->
        <a-space wrap>
          <a-button type="primary" @click="setTestToken">
            设置测试Token
          </a-button>
          <a-button @click="clearToken">
            清除Token
          </a-button>
          <a-button type="primary" @click="testSystemUser">
            测试 /system/user
          </a-button>
          <a-button type="primary" @click="testSystemRole">
            测试 /system/role
          </a-button>
          <a-button type="primary" @click="testMonitorOnline">
            测试 /monitor/online
          </a-button>
          <a-button type="dashed" @click="runAutoTest">
            自动测试
          </a-button>
          <a-button type="primary" @click="setTokenAndRefresh">
            设置Token并刷新
          </a-button>
          <a-button type="dashed" @click="testClientRoutes">
            测试客户端路由加载
          </a-button>
        </a-space>

        <!-- 路由列表 -->
        <a-card title="当前所有路由" size="small">
          <div class="route-list">
            <div v-for="route in allRoutes" :key="route.path" class="route-item">
              <a-tag size="small">{{ route.name || '无名称' }}</a-tag>
              <span class="route-path">{{ route.path }}</span>
            </div>
          </div>
        </a-card>

        <!-- 测试结果 -->
        <a-card title="测试日志" size="small">
          <div class="log-container">
            <div v-for="(log, index) in logs" :key="index" class="log-item">
              <span class="log-time">{{ log.time }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </a-card>
      </a-space>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getToken, setToken, clearToken as clearAuthToken } from '@/utils/auth'

defineOptions({ name: 'TokenTest' })

const router = useRouter()
const logs = ref<Array<{ time: string, message: string }>>([])

// 计算属性
const hasToken = computed(() => !!getToken())
const token = computed(() => getToken())
const routeFlag = computed(() => {
  // 检查是否有动态路由
  const routes = router.getRoutes()
  return routes.some(route => route.name === 'SystemUser' || route.name === 'SystemRole')
})

const allRoutes = computed(() => {
  return router.getRoutes().map(route => ({
    name: route.name,
    path: route.path
  })).slice(0, 20) // 只显示前20个路由
})

// 添加日志
const addLog = (message: string) => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message
  })
  if (logs.value.length > 20) {
    logs.value = logs.value.slice(0, 20)
  }
}

// 设置测试Token
const setTestToken = () => {
  const testToken = 'test-token-' + Date.now()
  setToken(testToken)
  addLog(`设置测试Token: ${testToken}`)
  addLog('Token已设置，请刷新页面以触发动态路由加载')
  
  // 1秒后自动刷新页面
  setTimeout(() => {
    window.location.reload()
  }, 1000)
}

// 清除Token
const clearToken = () => {
  clearAuthToken()
  addLog('已清除Token')
  addLog('Token已清除，请刷新页面')
  
  // 1秒后自动刷新页面
  setTimeout(() => {
    window.location.reload()
  }, 1000)
}

// 测试路由
const testSystemUser = async () => {
  try {
    addLog('尝试导航到 /system/user')
    await router.push('/system/user')
    addLog('成功导航到 /system/user')
  } catch (error: any) {
    addLog(`导航失败: ${error.message}`)
  }
}

const testSystemRole = async () => {
  try {
    addLog('尝试导航到 /system/role')
    await router.push('/system/role')
    addLog('成功导航到 /system/role')
  } catch (error: any) {
    addLog(`导航失败: ${error.message}`)
  }
}

const testMonitorOnline = async () => {
  try {
    addLog('尝试导航到 /monitor/online')
    await router.push('/monitor/online')
    addLog('成功导航到 /monitor/online')
  } catch (error: any) {
    addLog(`导航失败: ${error.message}`)
  }
}

// 自动测试
const runAutoTest = async () => {
  addLog('=== 开始自动测试 ===')

  // 1. 检查当前状态
  addLog(`当前Token: ${getToken() || '无'}`)
  addLog(`当前路由数量: ${router.getRoutes().length}`)

  // 2. 设置Token（如果没有的话）
  if (!getToken()) {
    const testToken = 'test-token-auto-' + Date.now()
    setToken(testToken)
    addLog(`设置测试Token: ${testToken}`)
  }

  // 3. 手动触发路由加载
  try {
    addLog('手动触发动态路由加载...')
    const { useRouteStore } = await import('@/stores')
    const routeStore = useRouteStore()

    const accessRoutes = await routeStore.generateRoutes()
    addLog(`生成了 ${accessRoutes.length} 个动态路由`)

    let addedCount = 0
    accessRoutes.forEach((route) => {
      const isHttpPath = route.path.startsWith('http://') || route.path.startsWith('https://')
      if (!isHttpPath) {
        router.addRoute(route)
        addedCount++
        addLog(`添加路由: ${String(route.name)} (${route.path})`)
      }
    })

    addLog(`成功添加 ${addedCount} 个动态路由`)
    addLog(`当前总路由数量: ${router.getRoutes().length}`)

    // 4. 测试路由访问
    addLog('测试访问 /system/user...')
    await router.push('/system/user')
    addLog('成功导航到 /system/user')

  } catch (error: any) {
    addLog(`自动测试失败: ${error.message}`)
    console.error('Auto test error:', error)
  }

  addLog('=== 自动测试完成 ===')
}

// 设置Token并刷新页面
const setTokenAndRefresh = () => {
  const testToken = 'test-token-' + Date.now()
  setToken(testToken)
  addLog(`设置Token: ${testToken}`)
  addLog('即将刷新页面以测试客户端路由加载...')

  setTimeout(() => {
    window.location.reload()
  }, 1000)
}

// 测试客户端路由加载
const testClientRoutes = async () => {
  addLog('=== 测试客户端路由加载 ===')

  try {
    // 1. 检查当前状态
    addLog(`当前Token: ${getToken() || '无'}`)
    addLog(`当前路由数量: ${router.getRoutes().length}`)

    // 2. 如果没有 token，先设置一个
    if (!getToken()) {
      const testToken = 'test-token-client-' + Date.now()
      setToken(testToken)
      addLog(`设置测试Token: ${testToken}`)
    }

    // 3. 手动触发客户端路由加载
    addLog('手动触发客户端路由加载...')
    const { useRouteStore } = await import('@/stores')
    const routeStore = useRouteStore()

    const accessRoutes = await routeStore.generateRoutes()
    addLog(`生成了 ${accessRoutes.length} 个动态路由`)

    let addedCount = 0
    accessRoutes.forEach((route) => {
      const isHttpPath = route.path.startsWith('http://') || route.path.startsWith('https://')
      if (!isHttpPath) {
        router.addRoute(route)
        addedCount++
        addLog(`添加路由: ${String(route.name)} (${route.path})`)
      }
    })

    addLog(`成功添加 ${addedCount} 个动态路由`)
    addLog(`当前总路由数量: ${router.getRoutes().length}`)

    // 4. 测试路由访问
    addLog('测试访问 /system/user...')
    await router.push('/system/user')
    addLog('✅ 成功导航到 /system/user')

  } catch (error: any) {
    addLog(`❌ 客户端路由测试失败: ${error.message}`)
    console.error('Client route test error:', error)
  }

  addLog('=== 客户端路由测试完成 ===')
}

onMounted(() => {
  addLog('Token测试工具已加载')
  addLog(`当前Token状态: ${hasToken.value ? '有' : '无'}`)
  addLog(`当前路由数量: ${allRoutes.value.length}`)
  addLog(`动态路由状态: ${routeFlag.value ? '已加载' : '未加载'}`)
})
</script>

<style scoped lang="scss">
.token-test {
  padding: 20px;
}

.route-list {
  max-height: 300px;
  overflow-y: auto;
}

.route-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  
  .route-path {
    margin-left: 10px;
    color: #666;
    font-family: monospace;
    font-size: 12px;
  }
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
}

.log-item {
  margin-bottom: 5px;
  font-family: monospace;
  font-size: 12px;
}

.log-time {
  color: #666;
  margin-right: 10px;
}

.log-message {
  color: #333;
}
</style>
