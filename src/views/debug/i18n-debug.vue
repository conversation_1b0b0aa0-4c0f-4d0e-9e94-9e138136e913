<template>
  <div class="i18n-debug">
    <h1>i18n 调试页面</h1>
    
    <div class="debug-section">
      <h2>基本信息</h2>
      <pre>{{ debugInfo }}</pre>
    </div>
    
    <div class="debug-section">
      <h2>翻译测试</h2>
      <div>
        <p>直接调用 t('common.confirm'): {{ t('common.confirm') }}</p>
        <p>直接调用 t('login.title'): {{ t('login.title') }}</p>
        <p>不存在的键 t('not.exist'): {{ t('not.exist') }}</p>
      </div>
    </div>
    
    <div class="debug-section">
      <h2>手动切换测试</h2>
      <button @click="manualSwitchToChinese">手动切换到中文</button>
      <button @click="manualSwitchToEnglish">手动切换到英文</button>
      <button @click="checkI18nState">检查 i18n 状态</button>
    </div>
    
    <div class="debug-section">
      <h2>LocaleSwitch 组件</h2>
      <LocaleSwitch />
    </div>
    
    <div class="debug-section">
      <h2>实时状态</h2>
      <p>当前语言: {{ currentLocale }}</p>
      <p>i18n.global.locale: {{ i18nGlobalLocale }}</p>
      <p>localStorage: {{ localStorageLocale }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import useLocale from '@/hooks/useLocale'
import LocaleSwitch from '@/components/LocaleSwitch/index.vue'

const { t, locale } = useI18n()
const { currentLocale, changeLocale } = useLocale()

const debugInfo = ref({})

const i18nGlobalLocale = computed(() => locale.value)
const localStorageLocale = computed(() => localStorage.getItem('continew-locale'))

const updateDebugInfo = () => {
  debugInfo.value = {
    'vue-i18n version': '9.x',
    'current locale': locale.value,
    'available locales': Object.keys((window as any).__i18n__?.global.messages || {}),
    'messages loaded': !!(window as any).__i18n__?.global.messages,
    'localStorage': localStorage.getItem('continew-locale'),
    'document.lang': document.documentElement.lang,
  }
}

const manualSwitchToChinese = () => {
  console.log('=== 手动切换到中文 ===')
  console.log('切换前 locale.value:', locale.value)
  
  locale.value = 'zh-CN'
  localStorage.setItem('continew-locale', 'zh-CN')
  
  console.log('切换后 locale.value:', locale.value)
  updateDebugInfo()
}

const manualSwitchToEnglish = () => {
  console.log('=== 手动切换到英文 ===')
  console.log('切换前 locale.value:', locale.value)
  
  locale.value = 'en-US'
  localStorage.setItem('continew-locale', 'en-US')
  
  console.log('切换后 locale.value:', locale.value)
  updateDebugInfo()
}

const checkI18nState = () => {
  console.log('=== i18n 状态检查 ===')
  console.log('locale.value:', locale.value)
  console.log('currentLocale.value:', currentLocale.value)
  console.log('t("common.confirm"):', t('common.confirm'))
  console.log('全局 i18n:', (window as any).__i18n__)
  updateDebugInfo()
}

onMounted(() => {
  updateDebugInfo()
  console.log('i18n 调试页面已加载')
  console.log('初始 locale:', locale.value)
})
</script>

<style scoped>
.i18n-debug {
  padding: 20px;
  max-width: 800px;
}

.debug-section {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.debug-section h2 {
  margin-top: 0;
  color: #333;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}

button {
  margin-right: 10px;
  margin-bottom: 10px;
  padding: 8px 16px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background: #40a9ff;
}
</style>
