<template>
  <div class="simple-test">
    <h1>简单 i18n 测试</h1>
    
    <div class="test-content">
      <h2>翻译测试</h2>
      <p>确认: {{ t('common.confirm') }}</p>
      <p>取消: {{ t('common.cancel') }}</p>
      <p>登录标题: {{ t('login.title') }}</p>
      
      <h2>当前状态</h2>
      <p>当前语言: {{ locale }}</p>
      
      <h2>切换测试</h2>
      <button @click="switchToZh">中文</button>
      <button @click="switchToEn">English</button>
      
      <h2>组件测试</h2>
      <LocaleSwitch />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import LocaleSwitch from '@/components/LocaleSwitch/index.vue'

const { t, locale } = useI18n()

const switchToZh = () => {
  locale.value = 'zh-CN'
  localStorage.setItem('continew-locale', 'zh-CN')
}

const switchToEn = () => {
  locale.value = 'en-US'
  localStorage.setItem('continew-locale', 'en-US')
}
</script>

<style scoped>
.simple-test {
  padding: 20px;
}

.test-content {
  max-width: 600px;
}

button {
  margin: 5px;
  padding: 10px 20px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background: #40a9ff;
}
</style>
