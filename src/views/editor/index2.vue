<template>
  <div class="editor-container">
    <div id="editor-holder"></div>
    <button @click="saveData">保存配置</button>
    <div class="preview" v-html="previewHtml"></div>
  </div>
</template>

<script setup>
import EditorJS from '@editorjs/editorjs'
import { onMounted, ref } from 'vue'
import { useEditor } from '@/composables/useEditor'
import { blockConfig } from '@/utils/blockTemplates'

const previewHtml = ref('')
const { editor, renderHtml } = useEditor()

const initEditor = async () => {
  editor.value = new EditorJS({
    holder: 'editor-holder',
    tools: blockConfig.tools,
    data: blockConfig.defaultData,
    onChange: async () => {
      const data = await editor.value.save()
      previewHtml.value = renderHtml(data)
    },
  })
}

onMounted(() => {
  initEditor()
})

const saveData = async () => {
  const savedData = await editor.value.save()
  console.log('配置数据:', savedData)
  // 这里可以添加API保存逻辑
}
</script>

<style scoped>
.editor-container {
  display: grid;
  grid-template-columns: 60% 40%;
}
#editor-holder {
  border: 1px solid #eee;
  padding: 20px;
}
.preview {
  border: 1px dashed #ccc;
  padding: 20px;
}
</style>
