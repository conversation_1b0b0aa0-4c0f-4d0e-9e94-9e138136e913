<template>
  <div id="editorJs" class="editor-demo"></div>
</template>

<script setup>
import EditorJS from '@editorjs/editorjs'
import Header from '@editorjs/header'; 
import List from '@editorjs/list'; 

const editor = new EditorJS({
  /**
   * Id of Element that should contain Editor instance
   */
  holder: 'editorjs',
  autofocus: true,
  tools: {
    paragraph: {
      config: {
        placeholder: '请输入',
      },
    },

    header: {
      class: Header,
      inlineToolbar: ['link'],
      config: {
        placeholder: 'Header',
      },
      shortcut: 'CMD+SHIFT+H',
    },

    /**
     * Or pass class directly without any configuration
     */
    // image: ImageTool,

    // list: {
    //   class: NestedList,
    //   inlineToolbar: true,
    //   shortcut: 'CMD+SHIFT+L',
    // },

    // checklist: {
    //   class: Checklist,
    //   inlineToolbar: true,
    // },

    // quote: {
    //   class: Quote,
    //   inlineToolbar: true,
    //   config: {
    //     quotePlaceholder: 'Enter a quote',
    //     captionPlaceholder: 'Quote\'s author',
    //   },
    //   shortcut: 'CMD+SHIFT+O',
    // },

    // marker: {
    //   class: Marker,
    //   shortcut: 'CMD+SHIFT+M',
    // },

    // code: {
    //   class: CodeTool,
    //   shortcut: 'CMD+SHIFT+C',
    // },

    // delimiter: Delimiter,

    // inlineCode: {
    //   class: InlineCode,
    //   shortcut: 'CMD+SHIFT+C',
    // },

    // linkTool: LinkTool,

    // embed: Embed,

    // table: {
    //   class: Table,
    //   inlineToolbar: true,
    //   shortcut: 'CMD+ALT+T',
    // },

  },

  /**
   * Initial Editor data
   */
  data: {},
  onReady() {
    console.log('Editor.js is ready to work!')
  },
})
</script>
