<template>
  <div class="editor-demo">
    <div id="editorjs" class="editor-container"></div>
    <a-button @click="save">保存</a-button>
    <div class="info-content">
    </div>
  </div>
</template>

<script setup>
import EditorJS from '@editorjs/editorjs'
import Header from '@editorjs/header'
import List from '@editorjs/list'

class SimpleImage {
  wrapper
  static get toolbox() {
    return {
      title: 'Image',
      icon: '<svg width="17" height="15" viewBox="0 0 336 276" xmlns="http://www.w3.org/2000/svg"><path d="M291 150V79c0-19-15-34-34-34H79c-19 0-34 15-34 34v42l67-44 81 72 56-29 42 30zm0 52l-43-30-56 30-81-67-66 39v23c0 19 15 34 34 34h178c17 0 31-13 34-29zM79 0h178c44 0 79 35 79 79v118c0 44-35 79-79 79H79c-44 0-79-35-79-79V79C0 35 35 0 79 0z"/></svg>',
    }
  }

  constructor({ data }) {
    this.data = data
    this.wrapper = undefined
  }

  _createImage(url, captionText) {
    const image = document.createElement('img')
    const caption = document.createElement('div')

    image.src = url
    caption.contentEditable = 'true'
    caption.innerHTML = captionText || ''

    this.wrapper.innerHTML = ''
    this.wrapper.appendChild(image)
    this.wrapper.appendChild(caption)
  }

  render() {
    this.wrapper = document.createElement('div')
    this.wrapper.classList.add('simple-image')

    if (this.data && this.data.url) {
      this._createImage(this.data.url, this.data.caption)
      return this.wrapper
    }

    const input = document.createElement('input')

    input.placeholder = 'Paste an image URL...'
    input.addEventListener('paste', (event) => {
      this._createImage(event?.clipboardData?.getData('text'))
    })

    this.wrapper.appendChild(input)

    return this.wrapper
  }

  save(blockContent) {
    const image = blockContent.querySelector('img')
    const caption = blockContent.querySelector('[contenteditable]')

    return {
      url: image.src,
      caption: caption.innerHTML || '',
    }
  }

  validate(savedData) {
    if (!savedData.url.trim()) {
      return false
    }

    return true
  }
}

// 自定义内容图片块组件
class ContentImageBlock {
  wrapper
  static get toolbox() {
    return {
      title: '内容图片块',
      icon: '<svg width="17" height="15" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M3 3h8v8H3V3zm10 0h8v8h-8V3zM3 13h8v8H3v-8zm10 0h8v8h-8v-8z"/></svg>',
    }
  }

  constructor({ data }) {
    this.data = data || { contentItems: [], images: [] }
    this.wrapper = undefined
  }

  _createContentItem(title, description, linkText, index) {
    const contentItem = document.createElement('div')
    contentItem.classList.add('content-item')
    contentItem.setAttribute('data-index', index)

    // 标题
    const titleElement = document.createElement('h3')
    titleElement.contentEditable = 'true'
    titleElement.innerHTML = title || '点击编辑标题'
    titleElement.classList.add('content-title')
    titleElement.addEventListener('click', (e) => e.stopPropagation())
    titleElement.addEventListener('blur', () => this._saveCurrentContent())
    titleElement.addEventListener('input', () => this._saveCurrentContent())

    // 描述
    const descElement = document.createElement('div')
    descElement.contentEditable = 'true'
    descElement.innerHTML = description || '点击编辑描述内容...'
    descElement.classList.add('content-description')
    descElement.addEventListener('click', (e) => e.stopPropagation())
    descElement.addEventListener('blur', () => this._saveCurrentContent())
    descElement.addEventListener('input', () => this._saveCurrentContent())

    // 链接按钮
    const linkElement = document.createElement('div')
    linkElement.contentEditable = 'true'
    linkElement.innerHTML = linkText || '了解更多 >'
    linkElement.classList.add('content-link')
    linkElement.addEventListener('click', (e) => e.stopPropagation())
    linkElement.addEventListener('blur', () => this._saveCurrentContent())
    linkElement.addEventListener('input', () => this._saveCurrentContent())

    // 删除按钮
    const deleteBtn = document.createElement('button')
    deleteBtn.innerHTML = '×'
    deleteBtn.classList.add('delete-btn')
    deleteBtn.addEventListener('click', (e) => {
      e.stopPropagation()
      this._removeContentItem(index)
    })

    contentItem.appendChild(titleElement)
    contentItem.appendChild(descElement)
    contentItem.appendChild(linkElement)
    contentItem.appendChild(deleteBtn)

    return contentItem
  }

  _createImageItem(imageUrl, index) {
    const imageItem = document.createElement('div')
    imageItem.classList.add('image-item')
    imageItem.setAttribute('data-index', index)

    const imageContainer = document.createElement('div')
    imageContainer.classList.add('image-container')

    if (imageUrl) {
      const image = document.createElement('img')
      image.src = imageUrl
      image.classList.add('content-image')
      image.addEventListener('click', (e) => {
        e.stopPropagation()
        this._editImage(imageContainer, index)
      })
      imageContainer.appendChild(image)
    } else {
      this._showImageInput(imageContainer, index)
    }

    // 删除按钮
    const deleteBtn = document.createElement('button')
    deleteBtn.innerHTML = '×'
    deleteBtn.classList.add('delete-btn')
    deleteBtn.addEventListener('click', (e) => {
      e.stopPropagation()
      this._removeImageItem(index)
    })

    imageItem.appendChild(imageContainer)
    imageItem.appendChild(deleteBtn)

    return imageItem
  }

  _saveCurrentContent() {
    // 保存当前编辑的内容到数据中
    const contentElements = this.wrapper.querySelectorAll('.content-item')
    const contentItems = []

    contentElements.forEach((element) => {
      const titleElement = element.querySelector('.content-title')
      const descElement = element.querySelector('.content-description')
      const linkElement = element.querySelector('.content-link')

      contentItems.push({
        title: titleElement ? titleElement.innerHTML : '',
        description: descElement ? descElement.innerHTML : '',
        linkText: linkElement ? linkElement.innerHTML : '',
      })
    })

    this.data.contentItems = contentItems
  }

  _addContentItem() {
    // 先保存当前内容
    this._saveCurrentContent()

    const contentItems = this.data.contentItems || []
    contentItems.push({
      title: '新标题',
      description: '新描述内容',
      linkText: '了解更多 >',
    })
    this.data.contentItems = contentItems
    this._renderContent()
  }

  _addImageItem() {
    // 先保存当前内容
    this._saveCurrentContent()

    const images = this.data.images || []
    images.push('')
    this.data.images = images
    this._renderContent()
  }

  _removeContentItem(index) {
    // 先保存当前内容
    this._saveCurrentContent()

    const contentItems = this.data.contentItems || []
    contentItems.splice(index, 1)
    this.data.contentItems = contentItems
    this._renderContent()
  }

  _removeImageItem(index) {
    // 先保存当前内容
    this._saveCurrentContent()

    const images = this.data.images || []
    images.splice(index, 1)
    this.data.images = images
    this._renderContent()
  }

  _editImage(container, index) {
    this._showImageInput(container, index)
  }

  _showImageInput(container, index) {
    container.innerHTML = ''
    const input = document.createElement('input')
    input.type = 'text'
    input.placeholder = '请输入图片URL...'
    input.classList.add('image-input')

    const handleImageInput = (url) => {
      if (url && url.trim()) {
        const images = this.data.images || []
        if (images[index] !== undefined) {
          images[index] = url.trim()
          this.data.images = images
        }

        const image = document.createElement('img')
        image.src = url.trim()
        image.classList.add('content-image')
        image.addEventListener('click', (e) => {
          e.stopPropagation()
          this._editImage(container, index)
        })
        container.innerHTML = ''
        container.appendChild(image)
      }
    }

    input.addEventListener('paste', () => {
      setTimeout(() => {
        handleImageInput(input.value)
      }, 10)
    })

    input.addEventListener('keypress', (event) => {
      if (event.key === 'Enter') {
        handleImageInput(input.value)
      }
    })

    input.addEventListener('blur', () => {
      handleImageInput(input.value)
    })

    container.appendChild(input)
    input.focus()
  }

  _renderContent() {
    this.wrapper.innerHTML = ''

    // 创建主容器
    const container = document.createElement('div')
    container.classList.add('content-image-container')

    // 创建左侧内容区域
    const leftContent = document.createElement('div')
    leftContent.classList.add('content-left')

    // 渲染所有内容项
    const contentItems = this.data.contentItems || []
    contentItems.forEach((item, index) => {
      const contentItem = this._createContentItem(item.title, item.description, item.linkText, index)
      leftContent.appendChild(contentItem)
    })

    // 创建右侧区域
    const rightContent = document.createElement('div')
    rightContent.classList.add('content-right')

    // 渲染所有图片
    const images = this.data.images || []
    images.forEach((imageUrl, index) => {
      const imageItem = this._createImageItem(imageUrl, index)
      rightContent.appendChild(imageItem)
    })

    // 添加操作按钮
    const actionButtons = document.createElement('div')
    actionButtons.classList.add('action-buttons')

    const addContentBtn = document.createElement('button')
    addContentBtn.innerHTML = '+ 添加内容'
    addContentBtn.classList.add('add-content-btn')
    addContentBtn.addEventListener('click', (e) => {
      e.stopPropagation()
      this._addContentItem()
    })

    const addImageBtn = document.createElement('button')
    addImageBtn.innerHTML = '+ 添加图片'
    addImageBtn.classList.add('add-image-btn')
    addImageBtn.addEventListener('click', (e) => {
      e.stopPropagation()
      this._addImageItem()
    })

    actionButtons.appendChild(addContentBtn)
    actionButtons.appendChild(addImageBtn)
    rightContent.appendChild(actionButtons)

    container.appendChild(leftContent)
    container.appendChild(rightContent)
    this.wrapper.appendChild(container)
  }

  render() {
    this.wrapper = document.createElement('div')
    this.wrapper.classList.add('content-image-block')

    // 如果没有数据，初始化默认数据
    if ((!this.data.contentItems || this.data.contentItems.length === 0) && (!this.data.images || this.data.images.length === 0)) {
      this.data = {
        contentItems: [
          {
            title: '耀华国际教育学校浙江桐乡校区',
            description: '耀华国际教育学校浙江桐乡校区成立于2017年，先进的校园设施确保学生在安全友好的环境中学习。学校坐落于高标准，提供从幼儿阶段至高中阶段（K2至13年级）的教育服务，招收2岁至18岁本地和外籍学生。',
            linkText: '了解更多 >',
          },
        ],
        images: ['https://via.placeholder.com/400x300/4A90E2/FFFFFF?text=School+Building'],
      }
    }

    this._renderContent()
    return this.wrapper
  }

  save(blockContent) {
    const contentItems = []
    const images = []

    // 收集左侧内容项
    const contentElements = blockContent.querySelectorAll('.content-item')
    contentElements.forEach((element) => {
      const titleElement = element.querySelector('.content-title')
      const descElement = element.querySelector('.content-description')
      const linkElement = element.querySelector('.content-link')

      contentItems.push({
        title: titleElement ? titleElement.innerHTML : '',
        description: descElement ? descElement.innerHTML : '',
        linkText: linkElement ? linkElement.innerHTML : '',
      })
    })

    // 收集右侧图片
    const imageElements = blockContent.querySelectorAll('.image-item .content-image')
    imageElements.forEach((element) => {
      images.push(element.src)
    })

    return { contentItems, images }
  }

  validate(savedData) {
    return (savedData.contentItems && savedData.contentItems.length > 0) || (savedData.images && savedData.images.length > 0)
  }
}

const editor = new EditorJS({
  /**
   * Id of Element that should contain Editor instance
   */
  holder: 'editorjs', // 容器id
  autofocus: true,
  tools: {
    paragraph: {
      config: {
        placeholder: '请输入',
      },
    },

    header: {
      class: Header,
      inlineToolbar: ['link'],
      config: {
        placeholder: 'Header',
        defaultLevel: 1, // 默认标题 // 默认创建的标题
        levels: [1, 2, 3, 4, 5], // 可转换的标题
      },
      shortcut: 'CMD+SHIFT+H',
    },

    list: List,

    image: {
      class: SimpleImage,
      inlineToolbar: true,
    }, // 自定义组件

    contentImageBlock: {
      class: ContentImageBlock,
      inlineToolbar: false,
    }, // 内容图片块组件
  },
  i18n: {
    messages: {
      ui: {
        blockTunes: {
          toggler: {
            'Click to tune': '点击转换',
            'or drag to move': '拖动调整',
          },
        },
        inlineToolbar: {
          converter: {
            'Convert to': '转换成',
          },
        },
        toolbar: {
          toolbox: {
            'Add': '添加',
            'Filter': '过滤',
            'Nothing found': '无内容',
          },
          popover: {
            'Filter': '过滤',
            'Nothing found': '无内容',
          },
        },
      },
      toolNames: {
        'Text': '段落',
        'Heading': '标题',
        'List': '列表',
        'Warning': '警告',
        'Checklist': '清单',
        'Quote': '引用',
        'Code': '代码',
        'Delimiter': '分割线',
        'Raw HTML': 'HTML片段',
        'Table': '表格',
        'Link': '链接',
        'Marker': '突出显示',
        'Bold': '加粗',
        'Italic': '倾斜',
        'InlineCode': '代码片段',
        'Image': '图片',
        '内容图片块': '内容图片块',
      },
      tools: {
        link: {
          'Add a link': '添加链接',
        },
        stub: {
          'The block can not be displayed correctly.': '该模块不能放置在这里',
        },
        image: {
          'Caption': '图片说明',
          'Select an Image': '选择图片',
          'With border': '添加边框',
          'Stretch image': '拉伸图像',
          'With background': '添加背景',
        },
        code: {
          'Enter a code': '输入代码',
        },
        linkTool: {
          'Link': '请输入链接地址',
          'Couldn\'t fetch the link data': '获取链接数据失败',
          'Couldn\'t get this link data, try the other one': '该链接不能访问，请修改',
          'Wrong response format from the server': '错误响应',
        },
        header: {
          'Header': '标题',
          'Heading 1': '一级标题',
          'Heading 2': '二级标题',
          'Heading 3': '三级标题',
          'Heading 4': '四级标题',
          'Heading 5': '五级标题',
        },
        paragraph: {
          'Enter something': '请输入笔记内容',
        },
        list: {
          Ordered: '有序列表',
          Unordered: '无序列表',
        },
        table: {
          'Heading': '标题',
          'Add column to left': '在左侧插入列',
          'Add column to right': '在右侧插入列',
          'Delete column': '删除列',
          'Add row above': '在上方插入行',
          'Add row below': '在下方插入行',
          'Delete row': '删除行',
          'With headings': '有标题',
          'Without headings': '无标题',
        },
        quote: {
          'Align Left': '左对齐',
          'Align Center': '居中对齐',
        },
      },
      blockTunes: {
        delete: {
          'Delete': '删除',
          'Click to delete': '点击删除',
        },
        moveUp: {
          'Move up': '向上移',
        },
        moveDown: {
          'Move down': '向下移',
        },
        filter: {
          Filter: '过滤',
        },
      },
    },
  },

  /**
   * Initial Editor data
   */
  onReady() {
    console.log('Editor.js is ready to work!')
  },

  // onChange(api, event) {
  //   console.log('Editor.js content changed:', api, event)
  // },
})

const save = () => {
  editor.save().then((outputData) => {
    console.log('Article data: ', outputData)
  }).catch((error) => {
    console.log('Saving failed: ', error)
  })
}
</script>

<style lang="scss">
.editor-demo {
  padding: 20px;

  .editor-container {
    background: #fff;
  }

  .simple-image img {
    max-width: 100%;
    margin-bottom: 15px;
  }

  .simple-image input {
    width: 100%;
    padding: 10px;
    border: 1px solid #e4e4e4;
    border-radius: 3px;
    outline: none;
    font-size: 14px;
  }

  .simple-image input,
  .simple-image [contenteditable] {
    // styles
    width: 100%;
    padding: 10px;
    border: 1px solid #e4e4e4;
    border-radius: 3px;
    outline: none;
    font-size: 14px;
  }

  // 内容图片块样式
  .content-image-block {
    margin: 20px 0;

    .content-image-container {
      display: flex;
      gap: 30px;
      align-items: flex-start;
      padding: 20px;
      border: 1px solid #e8e8eb;
      border-radius: 8px;
      background: #fff;

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 20px;
      }
    }

    .content-left {
      flex: 1;
      min-width: 0;

      .content-item {
        position: relative;
        margin-bottom: 20px;
        padding: 15px;
        border: 1px solid transparent;
        border-radius: 6px;
        transition: all 0.2s;

        &:hover {
          border-color: #e8e8eb;
          background-color: #f8f9fa;

          .delete-btn {
            opacity: 1;
          }
        }

        &:last-child {
          margin-bottom: 0;
        }

        .content-title {
          margin: 0 0 15px 0;
          font-size: 20px;
          font-weight: 600;
          color: #1a1a1a;
          line-height: 1.4;
          cursor: text;

          &:hover {
            background-color: rgba(74, 144, 226, 0.1);
          }

          &:focus {
            outline: 2px solid #4A90E2;
            outline-offset: 2px;
            background-color: #fff;
          }
        }

        .content-description {
          margin: 0 0 15px 0;
          font-size: 14px;
          line-height: 1.6;
          color: #666;
          cursor: text;
          min-height: 20px;

          &:hover {
            background-color: rgba(74, 144, 226, 0.1);
          }

          &:focus {
            outline: 2px solid #4A90E2;
            outline-offset: 2px;
            background-color: #fff;
          }
        }

        .content-link {
          display: inline-block;
          font-size: 14px;
          color: #4A90E2;
          cursor: text;
          padding: 5px 0;

          &:hover {
            background-color: rgba(74, 144, 226, 0.1);
            text-decoration: underline;
          }

          &:focus {
            outline: 2px solid #4A90E2;
            outline-offset: 2px;
            background-color: #fff;
          }
        }

        .delete-btn {
          position: absolute;
          top: -8px;
          right: -8px;
          width: 20px;
          height: 20px;
          border: none;
          border-radius: 50%;
          background: #ff4757;
          color: white;
          font-size: 12px;
          cursor: pointer;
          opacity: 0;
          transition: opacity 0.2s;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            background: #ff3838;
          }
        }
      }
    }

    .content-right {
      flex: 0 0 300px;

      @media (max-width: 768px) {
        flex: 1;
        width: 100%;
      }

      .image-item {
        position: relative;
        margin-bottom: 15px;

        &:hover {
          .delete-btn {
            opacity: 1;
          }
        }

        .image-container {
          .content-image {
            width: 100%;
            height: auto;
            border-radius: 6px;
            cursor: pointer;
            transition: opacity 0.2s;

            &:hover {
              opacity: 0.8;
            }
          }

          .image-input {
            width: 100%;
            padding: 15px;
            border: 2px dashed #ddd;
            border-radius: 6px;
            outline: none;
            font-size: 14px;
            text-align: center;
            background: #f9f9f9;

            &:focus {
              border-color: #4A90E2;
              background: #fff;
            }

            &::placeholder {
              color: #999;
            }
          }
        }

        .delete-btn {
          position: absolute;
          top: -8px;
          right: -8px;
          width: 20px;
          height: 20px;
          border: none;
          border-radius: 50%;
          background: #ff4757;
          color: white;
          font-size: 12px;
          cursor: pointer;
          opacity: 0;
          transition: opacity 0.2s;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            background: #ff3838;
          }
        }
      }

      .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 10px;
        margin-top: 15px;

        .add-content-btn, .add-image-btn {
          padding: 10px 15px;
          border: 2px dashed #ddd;
          border-radius: 6px;
          background: transparent;
          color: #666;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.2s;

          &:hover {
            border-color: #4A90E2;
            color: #4A90E2;
            background: #f8f9fa;
          }
        }
      }
    }
  }
}
:root {
  --color-bg-main: #F0F0F0;
  --color-border-light: #E8E8EB;
  --color-text-main: #000;
  --selectionColor: #e1f2ff;
}

.ce-popover {
  --border-radius: 6px;
  --width: 200px;
  --max-height: 270px;
  --padding: 6px;
  --offset-from-target: 8px;
  --color-border: #e8e8eb;
  --color-shadow: rgba(13,20,33,0.13);
  --color-background: white;
  --color-text-primary: black;
  --color-text-secondary: #707684;
  --color-border-icon: rgb(201 201 204 / 48%);
  --color-border-icon-disabled: #EFF0F1;
  --color-text-icon-active: #388AE5;
  --color-background-icon-active: rgba(56, 138, 229, 0.1);
  --color-background-item-focus: rgba(34, 186, 255, 0.08);
  --color-shadow-item-focus: rgba(7, 161, 227, 0.08);
  --color-background-item-hover: #eff2f5;
  --color-background-item-confirm: #E24A4A;
  --color-background-item-confirm-hover: #CE4343;
}
.dark-mode {
  --color-border-light: rgba(255, 255, 255,.08);
  --color-bg-main: #1c1e24;
  --color-text-main: #737886;
}
</style>
