<template>
  <div class="editor-demo">
    <div id="editorjs" class="editor-container"></div>
    <a-button @click="save">保存</a-button>
  </div>
</template>

<script setup>
import EditorJS from '@editorjs/editorjs'
import Header from '@editorjs/header'
import List from '@editorjs/list'

// class SimpleImage {
//   wrapper
//   static get toolbox() {
//     return {
//       title: 'Image',
//       icon: '<svg width="17" height="15" viewBox="0 0 336 276" xmlns="http://www.w3.org/2000/svg"><path d="M291 150V79c0-19-15-34-34-34H79c-19 0-34 15-34 34v42l67-44 81 72 56-29 42 30zm0 52l-43-30-56 30-81-67-66 39v23c0 19 15 34 34 34h178c17 0 31-13 34-29zM79 0h178c44 0 79 35 79 79v118c0 44-35 79-79 79H79c-44 0-79-35-79-79V79C0 35 35 0 79 0z"/></svg>',
//     }
//   }

//   constructor({ data }) {
//     this.data = data
//     this.wrapper = undefined
//   }

//   _createImage(url, captionText) {
//     const image = document.createElement('img')
//     const caption = document.createElement('div')

//     image.src = url
//     caption.contentEditable = 'true'
//     caption.innerHTML = captionText || ''

//     this.wrapper.innerHTML = ''
//     this.wrapper.appendChild(image)
//     this.wrapper.appendChild(caption)
//   }

//   render() {
//     this.wrapper = document.createElement('div')
//     this.wrapper.classList.add('simple-image')

//     if (this.data && this.data.url) {
//       this._createImage(this.data.url, this.data.caption)
//       return this.wrapper
//     }

//     const input = document.createElement('input')

//     input.placeholder = 'Paste an image URL...'
//     input.addEventListener('paste', (event) => {
//       this._createImage(event?.clipboardData?.getData('text'))
//     })

//     this.wrapper.appendChild(input)

//     return this.wrapper
//   }

//   save(blockContent) {
//     const image = blockContent.querySelector('img')
//     const caption = blockContent.querySelector('[contenteditable]')

//     return {
//       url: image.src,
//       caption: caption.innerHTML || '',
//     }
//   }

//   validate(savedData) {
//     if (!savedData.url.trim()) {
//       return false
//     }

//     return true
//   }
// }

// 自定义内容图片块组件
class ContentImageBlock {
  wrapper

  static get isReadOnlySupported() {
    return true
  }

  static get toolbox() {
    return {
      title: '内容图片块',
      icon: '<svg width="17" height="15" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M3 3h8v8H3V3zm10 0h8v8h-8V3zM3 13h8v8H3v-8zm10 0h8v8h-8v-8z"/></svg>',
    }
  }

  constructor({ data, api, config, readOnly }) {
    this.api = api
    this.data = this.extractData(data)
    this.wrapper = undefined
    this.readOnly = readOnly
    this.config = config || {}
    this.CSS = {
      blockConfig: 'block-config',
      configButton: 'config-button',
      configOverlay: 'config-overlay',
      configForm: 'config-form',
      sizeSelect: 'size-select',
      bgColorSelect: 'bg-color-select',
    }
  }

  extractData(inputData) {
    // 情况1: 已经是处理过的格式
    if (inputData && inputData.items && inputData.config) {
      return {
        items: inputData.items,
        config: inputData.config || {
          size: 'default',
          bgColor: 'white',
        },
      }
    }

    // 情况2: EditorJS原始数据格式
    if (inputData && inputData.blocks && Array.isArray(inputData.blocks)) {
      const contentBlock = inputData.blocks.find((block) => block.type === 'contentImageBlock')
      if (contentBlock && contentBlock.data) {
        return {
          items: contentBlock.data.items || [],
          config: contentBlock.data.config || {
            size: 'default',
            bgColor: 'white',
          },
        }
      }
    }

    // 情况3: 无效或空数据
    return {
      items: [],
      config: {
        size: 'default',
        bgColor: 'white',
      },
    }
  }

  _renderConfigButton() {
    if (this.readOnly) return null

    const configButton = document.createElement('button')
    configButton.innerHTML = '⚙️'
    configButton.classList.add(this.CSS.configButton)
    configButton.title = '配置区块'
    configButton.addEventListener('click', (e) => {
      e.stopPropagation()
      this._showConfigForm()
    })

    return configButton
  }

  _showConfigForm() {
    // 移除现有的配置表单
    const existingOverlay = document.querySelector(`.${this.CSS.configOverlay}`)
    if (existingOverlay) {
      existingOverlay.remove()
      return
    }

    const overlay = document.createElement('div')
    overlay.classList.add(this.CSS.configOverlay)

    const form = document.createElement('div')
    form.classList.add(this.CSS.configForm)

    // 容器大小选择
    const sizeLabel = document.createElement('label')
    sizeLabel.textContent = '容器大小:'
    const sizeSelect = document.createElement('select')
    sizeSelect.classList.add(this.CSS.sizeSelect)

    const sizes = [
      { value: 'large', label: '大' },
      { value: 'default', label: '默认' },
      { value: 'medium', label: '中' },
      { value: 'small', label: '小' },
    ]

    sizes.forEach((size) => {
      const option = document.createElement('option')
      option.value = size.value
      option.textContent = size.label
      sizeSelect.appendChild(option)
    })

    // 设置当前选中的大小
    sizeSelect.value = this.data.config?.size || 'default'

    // 背景色选择
    const bgColorLabel = document.createElement('label')
    bgColorLabel.textContent = '背景颜色:'
    const bgColorSelect = document.createElement('select')
    bgColorSelect.classList.add(this.CSS.bgColorSelect)

    const colors = [
      { value: 'white', label: '白色' },
      { value: 'gray', label: '灰色' },
    ]

    colors.forEach((color) => {
      const option = document.createElement('option')
      option.value = color.value
      option.textContent = color.label
      bgColorSelect.appendChild(option)
    })

    // 设置当前选中的背景色
    bgColorSelect.value = this.data.config?.bgColor || 'white'

    // 操作按钮
    const buttonGroup = document.createElement('div')
    buttonGroup.classList.add('button-group')

    const saveBtn = document.createElement('button')
    saveBtn.textContent = '保存'
    saveBtn.classList.add('save-btn')

    const cancelBtn = document.createElement('button')
    cancelBtn.textContent = '取消'
    cancelBtn.classList.add('cancel-btn')

    // 组装表单
    form.appendChild(sizeLabel)
    form.appendChild(sizeSelect)
    form.appendChild(bgColorLabel)
    form.appendChild(bgColorSelect)
    buttonGroup.appendChild(saveBtn)
    buttonGroup.appendChild(cancelBtn)
    form.appendChild(buttonGroup)
    overlay.appendChild(form)
    document.body.appendChild(overlay)

    saveBtn.addEventListener('click', () => {
      // 更新配置数据
      this.data.config = {
        size: sizeSelect.value,
        bgColor: bgColorSelect.value,
      }

      // 重新渲染组件以应用新配置
      this._renderContent()
      document.body.removeChild(overlay)
    })

    cancelBtn.addEventListener('click', () => {
      document.body.removeChild(overlay)
    })

    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        document.body.removeChild(overlay)
      }
    })

    sizeSelect.focus()
  }

  _createContentItem(title, description, linkText, linkUrl, linkType, index) {
    const contentItem = document.createElement('div')
    contentItem.classList.add('content-item', 'horizontal-item')
    contentItem.setAttribute('data-index', index)

    // 标题元素 - 根据 readOnly 状态决定是否可编辑
    const titleElement = document.createElement(this.readOnly ? 'div' : 'h3')
    titleElement.innerHTML = title || '点击编辑标题'
    titleElement.classList.add('content-title')
    if (!this.readOnly) {
      titleElement.contentEditable = 'true'
      titleElement.addEventListener('click', (e) => e.stopPropagation())
      titleElement.addEventListener('blur', () => this._saveCurrentContent())
      titleElement.addEventListener('input', () => this._saveCurrentContent())
      titleElement.addEventListener('paste', (e) => {
        e.stopPropagation()
        e.preventDefault()
        const text = e.clipboardData?.getData('text/plain') || ''
        if (text) {
          const selection = window.getSelection()
          if (selection && selection.rangeCount > 0) {
            const range = selection.getRangeAt(0)
            range.deleteContents()
            range.insertNode(document.createTextNode(text))
            range.collapse(false)
            selection.removeAllRanges()
            selection.addRange(range)
          }
        }
      })
      titleElement.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
          e.preventDefault()
          e.stopPropagation()
        }
      })
    }

    // 描述元素 - 根据 readOnly 状态决定是否可编辑
    const descElement = document.createElement('div')
    descElement.innerHTML = description || '点击编辑描述内容...'
    descElement.classList.add('content-description')
    if (!this.readOnly) {
      descElement.contentEditable = 'true'
      descElement.addEventListener('click', (e) => e.stopPropagation())
      descElement.addEventListener('blur', () => this._saveCurrentContent())
      descElement.addEventListener('input', () => this._saveCurrentContent())
      descElement.addEventListener('paste', (e) => {
        e.stopPropagation()
        e.preventDefault()
        const text = e.clipboardData?.getData('text/plain') || ''
        if (text) {
          const selection = window.getSelection()
          if (selection && selection.rangeCount > 0) {
            const range = selection.getRangeAt(0)
            range.deleteContents()
            range.insertNode(document.createTextNode(text))
            range.collapse(false)
            selection.removeAllRanges()
            selection.addRange(range)
          }
        }
      })
      descElement.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
          e.preventDefault()
          e.stopPropagation()
          // 允许在描述中换行
          const selection = window.getSelection()
          if (selection && selection.rangeCount > 0) {
            const range = selection.getRangeAt(0)
            range.deleteContents()
            range.insertNode(document.createElement('br'))
            range.collapse(false)
            selection.removeAllRanges()
            selection.addRange(range)
          }
        }
      })
    }

    // 链接按钮 - 添加链接类型支持
    const linkElement = document.createElement(this.readOnly ? 'a' : 'div')
    if (this.readOnly && linkElement instanceof HTMLAnchorElement) {
      linkElement.href = linkUrl || '#'
      linkElement.target = '_blank'
      linkElement.dataset.linkType = linkType || 'external'
    }
    linkElement.innerHTML = linkText || '了解更多 >'
    linkElement.classList.add('content-link')
    const linkData = Object.assign({}, this.data.items[index])
    if (!this.readOnly) {
      linkElement.addEventListener('click', (e) => {
        e.preventDefault()
        e.stopPropagation()
        this._editLink(linkElement, index, linkData)
      })
    }

    // 删除按钮 - 只在非只读模式下显示
    if (!this.readOnly) {
      const deleteBtn = document.createElement('button')
      deleteBtn.innerHTML = '×'
      deleteBtn.classList.add('delete-btn')
      deleteBtn.addEventListener('click', (e) => {
        e.stopPropagation()
        this._removeItem(index)
      })
      contentItem.appendChild(deleteBtn)
    }

    contentItem.appendChild(titleElement)
    contentItem.appendChild(descElement)
    contentItem.appendChild(linkElement)

    return contentItem
  }

  _editLink(linkElement, index, currentItem) {
    // 从组件数据源获取当前项

    const overlay = document.createElement('div')
    overlay.classList.add('link-edit-overlay')

    const form = document.createElement('div')
    form.classList.add('link-edit-form')

    // 链接类型选择
    const typeLabel = document.createElement('label')
    typeLabel.textContent = '链接类型:'
    const typeSelect = document.createElement('select')
    typeSelect.classList.add('link-type-select')

    const internalOption = document.createElement('option')
    internalOption.value = 'internal'
    internalOption.textContent = '内部链接'

    const externalOption = document.createElement('option')
    externalOption.value = 'external'
    externalOption.textContent = '外部链接'

    typeSelect.appendChild(internalOption)
    typeSelect.appendChild(externalOption)

    // 设置当前选中的类型
    typeSelect.value = currentItem.linkType || 'external'

    // 链接文本输入
    const textLabel = document.createElement('label')
    textLabel.textContent = '链接文本:'
    const textInput = document.createElement('input')
    textInput.type = 'text'
    textInput.value = currentItem.linkText || linkElement.innerHTML
    textInput.classList.add('link-text-input')

    // 链接地址输入
    const urlLabel = document.createElement('label')
    urlLabel.textContent = '链接地址:'
    const urlInput = document.createElement('input')
    urlInput.type = 'text'
    urlInput.value = currentItem.linkUrl || linkElement.getAttribute('href') || '#'
    urlInput.classList.add('link-url-input')

    // 操作按钮
    const buttonGroup = document.createElement('div')
    buttonGroup.classList.add('button-group')

    const saveBtn = document.createElement('button')
    saveBtn.textContent = '保存'
    saveBtn.classList.add('save-btn')

    const cancelBtn = document.createElement('button')
    cancelBtn.textContent = '取消'
    cancelBtn.classList.add('cancel-btn')

    // 组装表单
    form.appendChild(typeLabel)
    form.appendChild(typeSelect)
    form.appendChild(textLabel)
    form.appendChild(textInput)
    form.appendChild(urlLabel)
    form.appendChild(urlInput)
    buttonGroup.appendChild(saveBtn)
    buttonGroup.appendChild(cancelBtn)
    form.appendChild(buttonGroup)
    overlay.appendChild(form)
    document.body.appendChild(overlay)

    saveBtn.addEventListener('click', () => {
      const newType = typeSelect.value
      const newText = textInput.value || '了解更多 >'
      const newUrl = urlInput.value || '#'

      // 更新DOM元素
      linkElement.innerHTML = newText
      linkElement.setAttribute('href', newUrl)
      //   linkElement.dataset.linkType = newType // 存储链接类型到DOM

      // 更新组件数据
      const items = this.data.items || []
      if (items[index] && items[index].type === 'content') {
        items[index].linkType = newType
        items[index].linkText = newText
        items[index].linkUrl = newUrl
        this.data.items = items
      }

      document.body.removeChild(overlay)
    })

    cancelBtn.addEventListener('click', () => {
      document.body.removeChild(overlay)
    })

    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        document.body.removeChild(overlay)
      }
    })

    textInput.focus()
  }

  _createImageItem(item, index) {
    const imageItem = document.createElement('div')
    imageItem.classList.add('image-item', 'horizontal-item')
    imageItem.setAttribute('data-index', index)

    const imageContainer = document.createElement('div')
    imageContainer.classList.add('image-container')

    if (item.imageUrl) {
      const imageWrapper = document.createElement('div')
      imageWrapper.classList.add('image-wrapper')

      // 应用边框样式
      if (item.hasBorder) {
        imageWrapper.classList.add('has-border')
      }

      const image = document.createElement('img')
      image.src = item.imageUrl
      image.classList.add('content-image')
      image.alt = item.altText || '' // 设置alt属性

      // 如果有图片链接
      if (this.readOnly && item.imageLink) {
        const imageLink = document.createElement('a')
        imageLink.href = item.imageLink
        imageLink.target = '_blank'
        imageLink.appendChild(image)
        imageWrapper.appendChild(imageLink)
      } else {
        imageWrapper.appendChild(image)
      }

      // 如果有Caption
      if (item.caption) {
        const caption = document.createElement('div')
        caption.classList.add('image-caption')
        caption.textContent = item.caption
        imageWrapper.appendChild(caption)
      }

      if (!this.readOnly) {
        image.addEventListener('click', (e) => {
          e.stopPropagation()
          this._editImage(imageWrapper, index)
        })
      }

      imageContainer.appendChild(imageWrapper)
    } else if (!this.readOnly) {
      this._showImageInput(imageContainer, index)
    }

    // 删除按钮 - 只在非只读模式下显示
    if (!this.readOnly) {
      const deleteBtn = document.createElement('button')
      deleteBtn.innerHTML = '×'
      deleteBtn.classList.add('delete-btn')
      deleteBtn.addEventListener('click', (e) => {
        e.stopPropagation()
        this._removeItem(index)
      })
      imageItem.appendChild(deleteBtn)
    }

    imageItem.appendChild(imageContainer)
    return imageItem
  }

  _saveCurrentContent() {
    // 保存当前编辑的内容到数据中
    const allItems = this.wrapper.querySelectorAll('.horizontal-item')
    const items = []

    allItems.forEach((element) => {
      if (element.classList.contains('content-item')) {
        const titleElement = element.querySelector('.content-title')
        const descElement = element.querySelector('.content-description')
        const linkElement = element.querySelector('.content-link')

        items.push({
          type: 'content',
          title: titleElement ? titleElement.innerHTML : '',
          description: descElement ? descElement.innerHTML : '',
          linkText: linkElement ? linkElement.innerHTML : '',
          linkUrl: linkElement ? linkElement.getAttribute('href') || '#' : '#',
          linkType: linkElement ? linkElement.dataset.linkType || 'external' : 'external',
        })
      } else if (element.classList.contains('image-item')) {
        const imageElement = element.querySelector('.content-image')
        const imageWrapper = element.querySelector('.image-wrapper')
        const imageLink = element.querySelector('.image-wrapper a')
        const caption = element.querySelector('.image-caption')

        items.push({
          type: 'image',
          imageUrl: imageElement ? imageElement.src : '',
          hasBorder: imageWrapper ? imageWrapper.classList.contains('has-border') : false,
          imageLink: imageLink ? imageLink.href : null,
          altText: imageElement ? imageElement.alt : '',
          caption: caption ? caption.textContent : '',
        })
      }
    })

    this.data.items = items
  }

  _addContentItem() {
    // 先保存当前内容
    this._saveCurrentContent()

    const items = this.data.items || []
    items.push({
      type: 'content',
      title: '新标题',
      description: '新描述内容',
      linkText: '了解更多 >',
    })
    this.data.items = items
    this._renderContent()
  }

  _addImageItem() {
    // 先保存当前内容
    this._saveCurrentContent()

    const items = this.data.items || []
    items.push({
      type: 'image',
      imageUrl: '',
    })
    this.data.items = items
    this._renderContent()
  }

  _removeItem(index) {
    // 先保存当前内容
    this._saveCurrentContent()

    const items = this.data.items || []
    items.splice(index, 1)
    this.data.items = items
    this._renderContent()
  }

  _editImage(container, index) {
    // 从组件数据源获取当前项
    const currentItem = this.data.items[index]

    const overlay = document.createElement('div')
    overlay.classList.add('image-edit-overlay')

    const form = document.createElement('div')
    form.classList.add('image-edit-form')

    // 边框选择
    const borderLabel = document.createElement('label')
    borderLabel.textContent = '边框:'
    const borderSelect = document.createElement('select')
    borderSelect.classList.add('image-border-select')

    const noBorderOption = document.createElement('option')
    noBorderOption.value = 'no'
    noBorderOption.textContent = '无边框'

    const withBorderOption = document.createElement('option')
    withBorderOption.value = 'yes'
    withBorderOption.textContent = '有边框'

    borderSelect.appendChild(noBorderOption)
    borderSelect.appendChild(withBorderOption)
    borderSelect.value = currentItem.hasBorder ? 'yes' : 'no'

    // 图片链接
    const linkLabel = document.createElement('label')
    linkLabel.textContent = '图片链接:'
    const linkInput = document.createElement('input')
    linkInput.type = 'text'
    linkInput.value = currentItem.imageLink || ''
    linkInput.placeholder = '输入图片跳转链接 (可选)'
    linkInput.classList.add('image-link-input')

    // Alt文本
    const altLabel = document.createElement('label')
    altLabel.textContent = 'Alt文本:'
    const altInput = document.createElement('input')
    altInput.type = 'text'
    altInput.value = currentItem.altText || ''
    altInput.placeholder = '图片描述文本'
    altInput.classList.add('image-alt-input')

    // Caption
    const captionLabel = document.createElement('label')
    captionLabel.textContent = 'Caption:'
    const captionInput = document.createElement('input')
    captionInput.type = 'text'
    captionInput.value = currentItem.caption || ''
    captionInput.placeholder = '图片说明文字'
    captionInput.classList.add('image-caption-input')

    // 操作按钮
    const buttonGroup = document.createElement('div')
    buttonGroup.classList.add('button-group')

    const saveBtn = document.createElement('button')
    saveBtn.textContent = '保存'
    saveBtn.classList.add('save-btn')

    const cancelBtn = document.createElement('button')
    cancelBtn.textContent = '取消'
    cancelBtn.classList.add('cancel-btn')

    // 组装表单
    form.appendChild(borderLabel)
    form.appendChild(borderSelect)
    form.appendChild(linkLabel)
    form.appendChild(linkInput)
    form.appendChild(altLabel)
    form.appendChild(altInput)
    form.appendChild(captionLabel)
    form.appendChild(captionInput)
    buttonGroup.appendChild(saveBtn)
    buttonGroup.appendChild(cancelBtn)
    form.appendChild(buttonGroup)
    overlay.appendChild(form)
    document.body.appendChild(overlay)

    saveBtn.addEventListener('click', () => {
      // 更新组件数据 - 保留原有图片URL
      const items = this.data.items || []
      if (items[index] && items[index].type === 'image') {
        items[index] = {
          ...items[index], // 保留原有属性
          hasBorder: borderSelect.value === 'yes',
          imageLink: linkInput.value || null,
          altText: altInput.value || '',
          caption: captionInput.value || '',
        }
        this.data.items = items
      }

      // 重新渲染该项
      this._renderContent()
      document.body.removeChild(overlay)
    })

    cancelBtn.addEventListener('click', () => {
      document.body.removeChild(overlay)
    })

    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        document.body.removeChild(overlay)
      }
    })

    linkInput.focus()
  }

  _showImageInput(container, index) {
    container.innerHTML = ''
    const input = document.createElement('input')
    input.type = 'text'
    input.placeholder = '请输入图片URL...'
    input.classList.add('image-input')

    const handleImageInput = (url) => {
      if (url && url.trim()) {
        const items = this.data.items || []
        if (items[index] && items[index].type === 'image') {
          items[index].imageUrl = url.trim()
          this.data.items = items
        }

        const image = document.createElement('img')
        image.src = url.trim()
        image.classList.add('content-image')
        image.addEventListener('click', (e) => {
          e.stopPropagation()
          this._editImage(container, index)
        })
        container.innerHTML = ''
        container.appendChild(image)
      }
    }

    input.addEventListener('paste', () => {
      setTimeout(() => {
        handleImageInput(input.value)
      }, 10)
    })

    input.addEventListener('keypress', (event) => {
      if (event.key === 'Enter') {
        handleImageInput(input.value)
      }
    })

    input.addEventListener('blur', () => {
      handleImageInput(input.value)
    })

    container.appendChild(input)
    input.focus()
  }

  _renderContent() {
    this.wrapper.innerHTML = ''

    // 应用配置样式
    const container = document.createElement('div')
    container.classList.add('content-image-container')

    // 添加大小类
    container.classList.add(`size-${this.data.config?.size || 'default'}`)

    // 添加背景色类
    container.classList.add(`bg-${this.data.config?.bgColor || 'white'}`)

    // 添加配置按钮
    if (!this.readOnly) {
      const configButton = this._renderConfigButton()
      if (configButton) {
        container.appendChild(configButton)
      }
    }

    const horizontalContainer = document.createElement('div')
    horizontalContainer.classList.add('horizontal-container')

    const items = this.data.items || []
    items.forEach((item, index) => {
      let element
      if (item.type === 'content') {
        element = this._createContentItem(item.title, item.description, item.linkText, item.linkUrl, item.linkType, index)
      } else if (item.type === 'image') {
        element = this._createImageItem(item, index)
      }

      if (element) {
        horizontalContainer.appendChild(element)
      }
    })

    container.appendChild(horizontalContainer)

    // 操作按钮 - 只在非只读模式下显示
    if (!this.readOnly) {
      const actionButtons = document.createElement('div')
      actionButtons.classList.add('action-buttons')

      const addContentBtn = document.createElement('button')
      addContentBtn.innerHTML = '+ 添加内容'
      addContentBtn.classList.add('add-content-btn')
      addContentBtn.addEventListener('click', (e) => {
        e.stopPropagation()
        this._addContentItem()
      })

      const addImageBtn = document.createElement('button')
      addImageBtn.innerHTML = '+ 添加图片'
      addImageBtn.classList.add('add-image-btn')
      addImageBtn.addEventListener('click', (e) => {
        e.stopPropagation()
        this._addImageItem()
      })

      actionButtons.appendChild(addContentBtn)
      actionButtons.appendChild(addImageBtn)
      container.appendChild(actionButtons)
    }

    this.wrapper.appendChild(container)
  }

  render() {
    this.wrapper = document.createElement('div')
    this.wrapper.classList.add('content-image-block')

    // 确保数据格式正确
    if (!this.data.items || !Array.isArray(this.data.items)) {
      this.data.items = []
    }
    if (!this.data.config || typeof this.data.config !== 'object') {
      this.data.config = {
        size: 'default',
        bgColor: 'white',
      }
    }

    // 如果没有数据，初始化默认数据
    if (this.data.items.length === 0) {
      this.data.items = [
        {
          type: 'content',
          title: '耀华国际教育学校浙江桐乡校区',
          description: '耀华国际教育学校浙江桐乡校区成立于2017年...',
          linkText: '了解更多 >',
          linkType: 'external',
        },
        {
          type: 'image',
          imageUrl: 'https://via.placeholder.com/400x300/4A90E2/FFFFFF?text=School+Building',
          hasBorder: false,
          imageLink: null,
          altText: '',
          caption: '',
        },
      ]
    }

    this._renderContent()
    return this.wrapper
  }

  save(blockContent) {
    const items = []

    // 收集所有水平项目
    const allItems = blockContent.querySelectorAll('.horizontal-item')
    allItems.forEach((element) => {
      if (element.classList.contains('content-item')) {
        const titleElement = element.querySelector('.content-title')
        const descElement = element.querySelector('.content-description')
        const linkElement = element.querySelector('.content-link')

        items.push({
          type: 'content',
          title: titleElement ? titleElement.innerHTML : '',
          description: descElement ? descElement.innerHTML : '',
          linkText: linkElement ? linkElement.innerHTML : '',
          linkUrl: linkElement ? linkElement.getAttribute('href') || '#' : '#',
          linkType: linkElement ? linkElement.dataset.linkType || 'external' : 'external', // 保存链接类型
        })
      } else if (element.classList.contains('image-item')) {
        const imageElement = element.querySelector('.content-image')
        const imageWrapper = element.querySelector('.image-wrapper')
        const imageLink = element.querySelector('.image-wrapper a')
        const caption = element.querySelector('.image-caption')

        items.push({
          type: 'image',
          imageUrl: imageElement ? imageElement.src : '',
          hasBorder: imageWrapper ? imageWrapper.classList.contains('has-border') : false,
          imageLink: imageLink ? imageLink.href : null,
          altText: imageElement ? imageElement.alt : '',
          caption: caption ? caption.textContent : '',
        })
      }
    })

    return {
      items,
      config: this.data.config || {
        size: 'default',
        bgColor: 'white',
      },
    }
  }

  validate(savedData) {
    return savedData.items && savedData.items.length > 0
  }
}

const editor = new EditorJS({
  /**
   * Id of Element that should contain Editor instance
   */
  holder: 'editorjs', // 容器id
  autofocus: true,
  // readOnly: true,
  tools: {
    paragraph: {
      config: {
        placeholder: '请输入',
      },
    },

    header: {
      class: Header,
      inlineToolbar: ['link'],
      config: {
        placeholder: 'Header',
        defaultLevel: 1, // 默认标题 // 默认创建的标题
        levels: [1, 2, 3, 4, 5], // 可转换的标题
      },
      shortcut: 'CMD+SHIFT+H',
    },

    list: List,

    // image: {
    //   class: SimpleImage,
    //   inlineToolbar: true,
    // }, // 自定义组件

    contentImageBlock: {
      class: ContentImageBlock,
      inlineToolbar: true,
    }, // 内容图片块组件
  },
  i18n: {
    messages: {
      ui: {
        blockTunes: {
          toggler: {
            'Click to tune': '点击转换',
            'or drag to move': '拖动调整',
          },
        },
        inlineToolbar: {
          converter: {
            'Convert to': '转换成',
          },
        },
        toolbar: {
          toolbox: {
            'Add': '添加',
            'Filter': '过滤',
            'Nothing found': '无内容',
          },
          popover: {
            'Filter': '过滤',
            'Nothing found': '无内容',
          },
        },
      },
      toolNames: {
        'Text': '段落',
        'Heading': '标题',
        'List': '列表',
        'Warning': '警告',
        'Checklist': '清单',
        'Quote': '引用',
        'Code': '代码',
        'Delimiter': '分割线',
        'Raw HTML': 'HTML片段',
        'Table': '表格',
        'Link': '链接',
        'Marker': '突出显示',
        'Bold': '加粗',
        'Italic': '倾斜',
        'InlineCode': '代码片段',
        'Image': '图片',
        '内容图片块': '内容图片块',
      },
      tools: {
        link: {
          'Add a link': '添加链接',
        },
        stub: {
          'The block can not be displayed correctly.': '该模块不能放置在这里',
        },
        image: {
          'Caption': '图片说明',
          'Select an Image': '选择图片',
          'With border': '添加边框',
          'Stretch image': '拉伸图像',
          'With background': '添加背景',
        },
        code: {
          'Enter a code': '输入代码',
        },
        linkTool: {
          'Link': '请输入链接地址',
          'Couldn\'t fetch the link data': '获取链接数据失败',
          'Couldn\'t get this link data, try the other one': '该链接不能访问，请修改',
          'Wrong response format from the server': '错误响应',
        },
        header: {
          'Header': '标题',
          'Heading 1': '一级标题',
          'Heading 2': '二级标题',
          'Heading 3': '三级标题',
          'Heading 4': '四级标题',
          'Heading 5': '五级标题',
        },
        paragraph: {
          'Enter something': '请输入笔记内容',
        },
        list: {
          Ordered: '有序列表',
          Unordered: '无序列表',
        },
        table: {
          'Heading': '标题',
          'Add column to left': '在左侧插入列',
          'Add column to right': '在右侧插入列',
          'Delete column': '删除列',
          'Add row above': '在上方插入行',
          'Add row below': '在下方插入行',
          'Delete row': '删除行',
          'With headings': '有标题',
          'Without headings': '无标题',
        },
        quote: {
          'Align Left': '左对齐',
          'Align Center': '居中对齐',
        },
      },
      blockTunes: {
        delete: {
          'Delete': '删除',
          'Click to delete': '点击删除',
        },
        moveUp: {
          'Move up': '向上移',
        },
        moveDown: {
          'Move down': '向下移',
        },
        filter: {
          Filter: '过滤',
        },
      },
    },
  },
  data: {
    time: 1751168605257,
    blocks: [
      {
        id: '_AdstiatmJ',
        type: 'contentImageBlock',
        data: {
          items: [
            {
              type: 'content',
              title: '耀华国际教育学校浙江桐乡校区',
              description: '耀华国际教育学校浙江桐乡校区成立于2017年，先进的校园设施确保学生在安全友好的环境中学习。学校坐落于高标准，提供从幼儿阶段至高中阶段（K2至13年级）的教育服务，招收2岁至18岁本地和外籍学生。',
              linkText: '了解更多 &gt;',
              linkUrl: 'http://baidu.com',
              linkType: 'external',
            },
            {
              type: 'image',
              imageUrl: 'https://osswebsite.ycyw.com/media-library/ywies-bj/images/home/<USER>',
              hasBorder: false,
              imageLink: 'http://baidu.com',
              altText: '1',
              caption: '',
            },
          ],
          config: {
            size: 'default',
            bgColor: 'white',
          },
        },
      },
    ],
    version: '2.31.0-rc.7',
  },

  /**
   * Initial Editor data
   */
  onReady() {
    console.log('Editor.js is ready to work!')
  },

  // onChange(api, event) {
  //   console.log('Editor.js content changed:', api, event)
  // },
})

const save = () => {
  editor.save().then((outputData) => {
    console.log('Article data: ', outputData)
  }).catch((error) => {
    console.log('Saving failed: ', error)
  })
}
</script>

<style lang="scss">
.editor-demo {
  padding: 20px;

  .editor-container {
    background: #fff;
  }

  .simple-image img {
    max-width: 100%;
    margin-bottom: 15px;
  }

  .simple-image input {
    width: 100%;
    padding: 10px;
    border: 1px solid #e4e4e4;
    border-radius: 3px;
    outline: none;
    font-size: 14px;
  }

  .simple-image input,
  .simple-image [contenteditable] {
    // styles
    width: 100%;
    padding: 10px;
    border: 1px solid #e4e4e4;
    border-radius: 3px;
    outline: none;
    font-size: 14px;
  }

  // 内容图片块样式
  .content-image-block {
    margin: 20px 0;

    .content-image-container {
      padding: 20px;
      border: 1px solid #e8e8eb;
      border-radius: 8px;
      background: #fff;
    }

    .horizontal-container {
      display: flex;
      gap: 15px;
      align-items: flex-start;
      flex-wrap: wrap;
      margin-bottom: 20px;
    }

    .horizontal-item {
      position: relative;

      &.content-item {
        min-width: 200px;
        max-width: 280px;
        flex: 1;
        padding: 12px;
        border: 1px solid transparent;
        border-radius: 6px;
        transition: all 0.2s;
        background: #f9f9f9;

        &:hover {
          border-color: #e8e8eb;
          background-color: #f8f9fa;

          .delete-btn {
            opacity: 1;
          }
        }

      }

      &.image-item {
        min-width: 150px;
        max-width: 200px;
        height: 150px;
        flex: 0 0 auto;
        border: 1px solid transparent;
        border-radius: 6px;
        transition: all 0.2s;
        background: #f9f9f9;

        &:hover {
          border-color: #e8e8eb;
          background-color: #f8f9fa;

          .delete-btn {
            opacity: 1;
          }
        }
      }
    }

    .content-title {
      margin: 0 0 10px 0;
      font-size: 16px;
      font-weight: 600;
      color: #1a1a1a;
      line-height: 1.3;
      cursor: text;

      &:hover {
        background-color: rgba(74, 144, 226, 0.1);
      }

      &:focus {
        outline: 2px solid #4A90E2;
        outline-offset: 2px;
        background-color: #fff;
      }
    }

    .content-description {
      margin: 0 0 10px 0;
      font-size: 12px;
          line-height: 1.6;
          color: #666;
          cursor: text;
          min-height: 20px;

          &:hover {
            background-color: rgba(74, 144, 226, 0.1);
          }

          &:focus {
            outline: 2px solid #4A90E2;
            outline-offset: 2px;
            background-color: #fff;
          }
        }

    .content-link {
      display: inline-block;
      font-size: 12px;
      color: #4A90E2;
      cursor: text;
      padding: 3px 0;

      &:hover {
        background-color: rgba(74, 144, 226, 0.1);
        text-decoration: underline;
      }

      &:focus {
        outline: 2px solid #4A90E2;
        outline-offset: 2px;
        background-color: #fff;
      }
    }

    .delete-btn {
      position: absolute;
      top: -8px;
      right: -8px;
      width: 20px;
      height: 20px;
      border: none;
      border-radius: 50%;
      background: #ff4757;
      color: white;
      font-size: 12px;
      cursor: pointer;
      opacity: 0;
      transition: opacity 0.2s;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: #ff3838;
      }
    }

    .image-container {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .content-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 6px;
        cursor: pointer;
        transition: opacity 0.2s;

        &:hover {
          opacity: 0.8;
        }
      }

      .image-input {
        width: 100%;
        padding: 15px;
        border: 2px dashed #ddd;
        border-radius: 6px;
        outline: none;
        font-size: 14px;
        text-align: center;
        background: #f9f9f9;

        &:focus {
          border-color: #4A90E2;
          background: #fff;
        }

        &::placeholder {
          color: #999;
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 10px;
      justify-content: center;

      .add-content-btn, .add-image-btn {
        padding: 10px 15px;
        border: 2px dashed #ddd;
        border-radius: 6px;
        background: transparent;
        color: #666;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          border-color: #4A90E2;
          color: #4A90E2;
          background: #f8f9fa;
        }
      }
    }
  }
}
:root {
  --color-bg-main: #F0F0F0;
  --color-border-light: #E8E8EB;
  --color-text-main: #000;
  --selectionColor: #e1f2ff;
}

.ce-popover {
  --border-radius: 6px;
  --width: 200px;
  --max-height: 270px;
  --padding: 6px;
  --offset-from-target: 8px;
  --color-border: #e8e8eb;
  --color-shadow: rgba(13,20,33,0.13);
  --color-background: white;
  --color-text-primary: black;
  --color-text-secondary: #707684;
  --color-border-icon: rgb(201 201 204 / 48%);
  --color-border-icon-disabled: #EFF0F1;
  --color-text-icon-active: #388AE5;
  --color-background-icon-active: rgba(56, 138, 229, 0.1);
  --color-background-item-focus: rgba(34, 186, 255, 0.08);
  --color-shadow-item-focus: rgba(7, 161, 227, 0.08);
  --color-background-item-hover: #eff2f5;
  --color-background-item-confirm: #E24A4A;
  --color-background-item-confirm-hover: #CE4343;
}
.dark-mode {
  --color-border-light: rgba(255, 255, 255,.08);
  --color-bg-main: #1c1e24;
  --color-text-main: #737886;
}
.link-edit-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.link-edit-form {
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 400px;
  max-width: 90%;

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
  }

  input {
    width: 100%;
    padding: 8px;
    margin-bottom: 16px;
    border: 1px solid #ddd;
    border-radius: 4px;
  }
}

.link-edit-form select.link-type-select {
  width: 100%;
  padding: 8px;
  margin-bottom: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.link-edit-form label {
  display: block;
  margin: 10px 0 5px;
  font-weight: bold;
}

/* 图片编辑表单样式 */
.image-edit-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.image-edit-form {
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 400px;
  max-width: 90%;
}

.image-edit-form label {
  display: block;
  margin: 10px 0 5px;
  font-weight: bold;
}

.image-edit-form input[type="text"],
.image-edit-form select {
  width: 100%;
  padding: 8px;
  margin-bottom: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

/* 图片边框样式 */
.image-wrapper.has-border {
  border: 2px solid #e8e8eb;
  border-radius: 4px;
  padding: 5px;
}

/* 图片说明文字样式 */
.image-caption {
  text-align: center;
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 10px;

  button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;

    &.save-btn {
      background-color: #4A90E2;
      color: white;

      &:hover {
        background-color: #3a80d2;
      }
    }

    &.cancel-btn {
      background-color: #f0f0f0;

      &:hover {
        background-color: #e0e0e0;
      }
    }
  }
}

/* 配置按钮样式 */
.config-button {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  z-index: 10;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.config-button:hover {
  opacity: 1;
}

/* 配置表单样式 */
.config-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.config-form {
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 300px;
}

.config-form label {
  display: block;
  margin: 10px 0 5px;
  font-weight: bold;
}

.config-form select {
  width: 100%;
  padding: 8px;
  margin-bottom: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

/* 容器大小样式 */
.content-image-container.size-large {
  max-width: 1800px;
  margin: 0 auto;
}

.content-image-container.size-default {
  max-width: 1200px;
  margin: 0 auto;
}

.content-image-container.size-medium {
  max-width: 800px;
  margin: 0 auto;
}

.content-image-container.size-small {
  max-width: 600px;
  margin: 0 auto;
}

/* 背景颜色样式 */
.content-image-container.bg-white {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
}

.content-image-container.bg-gray {
  background-color: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
}
</style>
