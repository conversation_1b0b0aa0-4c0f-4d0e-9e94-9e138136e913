<template>
  <div class="editor-container">
    <div id="editorjs" class="editor-holder"></div>

    <div class="editor-actions">
      <button class="save-button" @click="handleSave">
        Save Content
      </button>
      <button class="clear-button" @click="handleClear">
        Clear Editor
      </button>
    </div>

    <div v-if="savedData" class="output-container">
      <h3 class="output-title">Saved Data:</h3>
      <pre class="output-content">{{ JSON.stringify(savedData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import EditorJS from '@editorjs/editorjs'
import Header from '@editorjs/header'
import Paragraph from '@editorjs/paragraph'
import Image from '@editorjs/image'
import Button from 'editorjs-button'
// import Layout from 'editorjs-layout'
import { LayoutBlockTool } from 'editorjs-layout'

// 自定义组件工具 (例如 MyCustomComponent.js)
class MyCustomComponent {
  static get toolbox() {
    return {
      title: 'Custom Component',
      icon: '<svg>...</svg>',
    }
  }

  constructor({ data, api }) {
    this.data = data || {}
    this.api = api
  }

  render() {
    const wrapper = document.createElement('div')
    wrapper.innerHTML = `
      <div class="my-custom-component">
        <h3>自定义组件</h3>
        <input type="text" value="${this.data.text || ''}" placeholder="输入内容">
      </div>
    `
    return wrapper
  }

  save(blockContent) {
    return {
      text: blockContent.querySelector('input').value,
    }
  }
}

const editor = new EditorJS({
  /**
   * Id of Element that should contain Editor instance
   */
  holder: 'editorjs', // 容器id
  autofocus: true,
  // readOnly: true,
  tools: {
    header: {
      class: Header,
      config: {
        placeholder: 'Enter a header',
        levels: [1, 2, 3],
        defaultLevel: 2,
      },
    },
    paragraph: {
      class: Paragraph,
      config: {
        placeholder: 'Enter your text here',
      },
    },
    image: {
      class: Image,
      config: {
        endpoints: {
          byFile: 'http://localhost:8008/uploadFile',
          byUrl: 'http://localhost:8008/fetchUrl',
        },
      },
    },
    // button: {
    //   class: Button,
    //   config: {
    //     placeholder: 'Enter button text',
    //     buttonText: 'Click me',
    //     style: {
    //       backgroundColor: '#3F51B5',
    //       color: '#FFFFFF',
    //       borderRadius: '4px',
    //       padding: '10px 20px',
    //     },
    //   },
    // },
    layout: {
      class: LayoutBlockTool,
      config: {
        EditorJS,
        editorJSConfig: {},
        enableLayoutEditing: false,
        enableLayoutSaving: true,
        initialData: {
          itemContent: {
            1: {
              blocks: [],
            },
          },
          layout: {
            type: 'container',
            id: '',
            className: '',
            style: 'border: 1px solid #000000;',
            children: [
              {
                type: 'item',
                id: '',
                className: '',
                style: 'border: 1px solid #000000; display: inline-block;',
                itemContentId: '1',
              },
            ],
          },
        },
      },
    },
    myCustomComponent: MyCustomComponent,
    twoColumns: {
      class: LayoutBlockTool,
      config: {
        EditorJS,
        editorJSConfig: {},
        enableLayoutEditing: false,
        enableLayoutSaving: false,
        initialData: {
          itemContent: {
            1: {
              blocks: [
                {
                  type: 'myCustomComponent', // 使用你的自定义组件
                  data: { text: '左侧自定义内容' },
                },
              ],
            },
            2: {
              blocks: [],
            },
          },
          layout: {
            type: 'container',
            id: '',
            className: '',
            style: 'border: 1px solid #000000; display: flex; justify-content: space-around; padding: 16px;',
            children: [
              {
                type: 'item',
                id: '',
                className: '',
                style: 'border: 1px solid #000000; padding: 8px;',
                itemContentId: '1',
              },
              {
                type: 'item',
                id: '',
                className: '',
                style: 'border: 1px solid #000000; padding: 8px;',
                itemContentId: '2',
              },
            ],
          },
        },
      },
      toolbox: {
        icon: `<svg xmlns='http://www.w3.org/2000/svg' width="16" height="16" viewBox='0 0 512 512'>
                    <rect x='128' y='128' width='336' height='336' rx='57' ry='57' fill='none' stroke='currentColor' stroke-linejoin='round' stroke-width='32'/>
                    <path d='M383.5 128l.5-24a56.16 56.16 0 00-56-56H112a64.19 64.19 0 00-64 64v216a56.16 56.16 0 0056 56h24' fill='none' stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' stroke-width='32'/>
                  </svg>`,
        title: '2 columns',
      },
      shortcut: 'CMD+2',
    },
  },
  i18n: {
    messages: {
      ui: {
        blockTunes: {
          toggler: {
            'Click to tune': '点击转换',
            'or drag to move': '拖动调整',
          },
        },
        inlineToolbar: {
          converter: {
            'Convert to': '转换成',
          },
        },
        toolbar: {
          toolbox: {
            'Add': '添加',
            'Filter': '过滤',
            'Nothing found': '无内容',
          },
          popover: {
            'Filter': '过滤',
            'Nothing found': '无内容',
          },
        },
      },
      toolNames: {
        'Text': '段落',
        'Heading': '标题',
        'List': '列表',
        'Warning': '警告',
        'Checklist': '清单',
        'Quote': '引用',
        'Code': '代码',
        'Delimiter': '分割线',
        'Raw HTML': 'HTML片段',
        'Table': '表格',
        'Link': '链接',
        'Marker': '突出显示',
        'Bold': '加粗',
        'Italic': '倾斜',
        'InlineCode': '代码片段',
        'Image': '图片',
        '内容图片块': '内容图片块',
      },
      tools: {
        link: {
          'Add a link': '添加链接',
        },
        stub: {
          'The block can not be displayed correctly.': '该模块不能放置在这里',
        },
        image: {
          'Caption': '图片说明',
          'Select an Image': '选择图片',
          'With border': '添加边框',
          'Stretch image': '拉伸图像',
          'With background': '添加背景',
        },
        code: {
          'Enter a code': '输入代码',
        },
        linkTool: {
          'Link': '请输入链接地址',
          'Couldn\'t fetch the link data': '获取链接数据失败',
          'Couldn\'t get this link data, try the other one': '该链接不能访问，请修改',
          'Wrong response format from the server': '错误响应',
        },
        header: {
          'Header': '标题',
          'Heading 1': '一级标题',
          'Heading 2': '二级标题',
          'Heading 3': '三级标题',
          'Heading 4': '四级标题',
          'Heading 5': '五级标题',
        },
        paragraph: {
          'Enter something': '请输入笔记内容',
        },
        list: {
          Ordered: '有序列表',
          Unordered: '无序列表',
        },
        table: {
          'Heading': '标题',
          'Add column to left': '在左侧插入列',
          'Add column to right': '在右侧插入列',
          'Delete column': '删除列',
          'Add row above': '在上方插入行',
          'Add row below': '在下方插入行',
          'Delete row': '删除行',
          'With headings': '有标题',
          'Without headings': '无标题',
        },
        quote: {
          'Align Left': '左对齐',
          'Align Center': '居中对齐',
        },
      },
      blockTunes: {
        delete: {
          'Delete': '删除',
          'Click to delete': '点击删除',
        },
        moveUp: {
          'Move up': '向上移',
        },
        moveDown: {
          'Move down': '向下移',
        },
        filter: {
          Filter: '过滤',
        },
      },
    },
  },
  data: {
    blocks: [
      {
        type: 'layout',
        data: {
          layout: '1-1',
          cols: [
            [
              // 左侧列内容
              {
                type: 'header',
                data: {
                  text: '产品标题',
                  level: 2,
                },
              },
              {
                type: 'paragraph',
                data: {
                  text: '这里是产品描述文本，可以详细介绍产品的特点和优势。这段文字将显示在布局的左侧。',
                },
              },
              {
                type: 'button',
                data: {
                  text: '立即购买',
                  link: '#purchase',
                  style: {
                    backgroundColor: '#FF5722',
                    color: '#FFFFFF',
                  },
                },
              },
            ],
            [
              // 右侧列内容
              {
                type: 'image',
                data: {
                  url: 'https://via.placeholder.com/600x400?text=Product+Image',
                  caption: '产品展示图',
                  withBorder: false,
                  stretched: true,
                  withBackground: false,
                },
              },
            ],
          ],
        },
      },
    ],
  },

  /**
   * Initial Editor data
   */
  onReady() {
    console.log('Editor.js is ready to work!')
  },

  // onChange(api, event) {
  //   console.log('Editor.js content changed:', api, event)
  // },
})

const savedData = ref()
// 保存编辑器内容
const handleSave = () => {
  editor.save().then((outputData) => {
    console.log('Article data: ', outputData)
    savedData.value = outputData
  }).catch((error) => {
    console.log('Saving failed: ', error)
  })
}

// 清空编辑器
const handleClear = () => {
  editor?.clear()
  savedData.value = null
}
</script>

<style scoped lang="scss">
.editor-container {
  width: 100%;
  margin: 0 auto;
  padding: 20px;
}

.editor-holder {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 24px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
}

.editor-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.save-button,
.clear-button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.save-button {
  background-color: #4CAF50;
  color: white;
}

.save-button:hover {
  background-color: #43A047;
}

.clear-button {
  background-color: #f5f5f5;
  color: #333;
}

.clear-button:hover {
  background-color: #e0e0e0;
}

.output-container {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e0e0e0;
}

.output-title {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 16px;
  color: #333;
}

.output-content {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  color: #666;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 编辑器内部样式 */
:deep(.ce-layout) {
  display: flex;
  gap: 32px;
  margin: 24px 0;
}

:deep(.ce-layout__col) {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

:deep(.cdx-button) {
  align-self: flex-start;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 8px;
}

:deep(.cdx-button:hover) {
  opacity: 0.9;
  transform: translateY(-1px);
}

:deep(.image-tool__image-picture) {
  width: 100%;
  height: auto;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  :deep(.ce-layout) {
    flex-direction: column;
    gap: 20px;
  }
}
</style>
