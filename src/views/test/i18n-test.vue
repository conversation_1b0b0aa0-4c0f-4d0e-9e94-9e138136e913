<template>
  <div class="i18n-test">
    <h1>i18n 测试页面</h1>

    <div class="test-section">
      <h2>当前状态</h2>
      <p>当前语言: {{ currentLocale }}</p>
      <p>i18n locale: {{ i18nLocale }}</p>
    </div>

    <div class="test-section">
      <h2>翻译测试</h2>
      <p>登录标题: {{ t('login.title') }}</p>
      <p>确认按钮: {{ t('common.confirm') }}</p>
      <p>取消按钮: {{ t('common.cancel') }}</p>
      <p>仪表盘: {{ t('menu.dashboard') }}</p>
      <p>退出登录: {{ t('navbar.logout') }}</p>
    </div>

    <div class="test-section">
      <h2>语言切换测试</h2>
      <button @click="changeToZh" style="margin-right: 10px;">切换到中文</button>
      <button @click="changeToEn" style="margin-right: 10px;">Switch to English</button>
      <button @click="forceReload" style="margin-right: 10px;">强制刷新</button>
    </div>

    <div class="test-section">
      <h2>LocaleSwitch 组件测试</h2>
      <LocaleSwitch />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import useLocale from '@/hooks/useLocale'
import LocaleSwitch from '@/components/LocaleSwitch/index.vue'

const { t, locale } = useI18n()
const { currentLocale, changeLocale } = useLocale()

const i18nLocale = computed(() => locale.value)

const changeToZh = () => {
  console.log('点击切换到中文')
  changeLocale('zh-CN')
}

const changeToEn = () => {
  console.log('点击切换到英文')
  changeLocale('en-US')
}

const forceReload = () => {
  window.location.reload()
}
</script>

<style scoped>
.i18n-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
}

.test-section h2 {
  margin-top: 0;
  color: #333;
}

button {
  padding: 8px 16px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background: #40a9ff;
}
</style>
