<template>
  <div class="i18n-test">
    <h1>{{ t('login.title') }}</h1>
    <p>{{ t('common.confirm') }}</p>
    <p>{{ t('common.cancel') }}</p>
    <p>{{ t('menu.dashboard') }}</p>
    <p>{{ t('navbar.logout') }}</p>
    
    <div>
      <h2>语言切换测试</h2>
      <button @click="changeToZh">切换到中文</button>
      <button @click="changeToEn">Switch to English</button>
      <p>当前语言: {{ currentLocale }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import useLocale from '@/hooks/useLocale'

const { t } = useI18n()
const { currentLocale, changeLocale } = useLocale()

const changeToZh = () => {
  changeLocale('zh-CN')
}

const changeToEn = () => {
  changeLocale('en-US')
}
</script>

<style scoped>
.i18n-test {
  padding: 20px;
}
</style>
