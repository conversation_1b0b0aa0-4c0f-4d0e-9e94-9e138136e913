<template>
  <div class="ssr-test">
    <h1>SSR 测试页面</h1>
    
    <div class="test-section">
      <h2>环境检测</h2>
      <p>当前环境: {{ environment }}</p>
      <p>是否为服务端渲染: {{ isSSR ? '是' : '否' }}</p>
      <p>是否为客户端: {{ isClient ? '是' : '否' }}</p>
    </div>
    
    <div class="test-section">
      <h2>国际化测试</h2>
      <p>确认按钮: {{ t('common.confirm') }}</p>
      <p>取消按钮: {{ t('common.cancel') }}</p>
      <p>当前语言: {{ currentLocale }}</p>
    </div>
    
    <div class="test-section">
      <h2>响应式数据测试</h2>
      <p>计数器: {{ count }}</p>
      <button @click="increment">增加</button>
      <button @click="decrement">减少</button>
    </div>
    
    <div class="test-section">
      <h2>客户端特定功能</h2>
      <p>当前时间: {{ currentTime }}</p>
      <p>窗口宽度: {{ windowWidth }}</p>
      <button @click="showAlert">显示警告框</button>
    </div>
    
    <div class="test-section">
      <h2>路由测试</h2>
      <p>当前路由: {{ $route.path }}</p>
      <router-link to="/login">跳转到登录页</router-link>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import useLocale from '@/hooks/useLocale'

defineOptions({ name: 'SSRTest' })

const { t } = useI18n()
const { currentLocale } = useLocale()

// 环境检测
const isSSR = typeof window === 'undefined'
const isClient = typeof window !== 'undefined'
const environment = computed(() => {
  if (isSSR) return '服务端'
  if (isClient) return '客户端'
  return '未知'
})

// 响应式数据
const count = ref(0)
const increment = () => count.value++
const decrement = () => count.value--

// 客户端特定功能
const currentTime = ref('')
const windowWidth = ref(0)

const updateTime = () => {
  currentTime.value = new Date().toLocaleTimeString()
}

const updateWindowWidth = () => {
  if (typeof window !== 'undefined') {
    windowWidth.value = window.innerWidth
  }
}

const showAlert = () => {
  if (typeof window !== 'undefined') {
    alert('这是一个客户端警告框！')
  }
}

// 客户端挂载后的操作
onMounted(() => {
  updateTime()
  updateWindowWidth()
  
  // 定时更新时间
  const timer = setInterval(updateTime, 1000)
  
  // 监听窗口大小变化
  const handleResize = () => updateWindowWidth()
  window.addEventListener('resize', handleResize)
  
  // 清理
  onUnmounted(() => {
    clearInterval(timer)
    window.removeEventListener('resize', handleResize)
  })
})
</script>

<style scoped lang="scss">
.ssr-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  background: #f9f9f9;
}

.test-section h2 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 10px;
}

button {
  margin: 5px;
  padding: 8px 16px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background: #40a9ff;
}

a {
  color: #1890ff;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}
</style>
