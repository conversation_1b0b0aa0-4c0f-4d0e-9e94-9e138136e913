<!-- 未完善 -->
<template>
  <div v-if="isClient" ref="divRef" class="container">
    <div class="aie-container">
      <div class="aie-header-panel" style="display: none;">
        <div class="aie-container-header"></div>
      </div>
      <div class="aie-main">
        <div class="aie-container-panel">
          <div class="aie-container-main"></div>
        </div>
      </div>
      <div class="aie-container-footer" style="display: none;"></div>
    </div>
  </div>
  <div v-else class="ssr-placeholder">
    <p>富文本编辑器加载中...</p>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores'

// 客户端检测
const isClient = ref(false)

// 动态导入 aieditor，仅在客户端加载
let AiEditor: any = null
let AiEditorOptions: any = null

defineOptions({ name: 'AiEditor' })
const props = defineProps<{
  modelValue: string
  options?: any
}>()
const aieditor = ref<any>(null)
const appStore = useAppStore()
const divRef = ref<any>()

const editorConfig = reactive<any>({
  element: '',
  theme: appStore.theme,
  placeholder: '请输入内容',
  content: '',
  editable: false,
})
const init = () => {
  if (!AiEditor || typeof window === 'undefined') return
  aieditor.value?.destroy()
  aieditor.value = new AiEditor(editorConfig)
}
watch(() => props.modelValue, (value) => {
  if (value !== aieditor.value?.getHtml()) {
    editorConfig.content = value
    init()
  }
}, { deep: true })
watch(() => appStore.theme, (value) => {
  editorConfig.theme = value
  init()
})

// 挂载阶段（仅客户端）
onMounted(async () => {
  // 标记为客户端环境
  isClient.value = true

  // 动态加载 aieditor 和样式
  try {
    const [editorModule] = await Promise.all([
      import('aieditor'),
      import('aieditor/dist/style.css')
    ])

    AiEditor = editorModule.AiEditor

    // 等待下一个 tick 确保 DOM 已更新
    await nextTick()

    editorConfig.element = divRef.value
    editorConfig.content = props.modelValue
    init()
  } catch (error) {
    console.error('Failed to load AiEditor:', error)
  }
})
// 销毁阶段
onUnmounted(() => {
  aieditor.value?.destroy()
})
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  width: 100%;
  box-sizing: border-box;
}

.aie-header-panel {
  position: sticky;
  // top: 51px;
  z-index: 1;
}

.aie-header-panel aie-header>div {
  align-items: center;
  justify-content: center;
  padding: 10px 0;
}

.aie-container {
  border: none !important;
}

.aie-container-panel {
  width: calc(100% - 2rem - 2px);
  max-width: 826.77px;
  margin: 0rem auto;
  border: 1px solid var(--color-border-1);
  background-color: var() rgba($color: var(--color-bg-1), $alpha: 1.0);
  height: 100%;
  padding: 1rem;
  z-index: 99;
  overflow: auto;
  box-sizing: border-box;
}

.aie-main {
  position: relative;
  overflow: hidden;
  flex: 1;
  box-sizing: border-box;
  padding: 1rem 0px;
  background-color: var(--color-bg-1);
}

.aie-directory {
  position: absolute;
  top: 30px;
  left: 10px;
  width: 260px;
  z-index: 0;

}

.aie-title1 {
  font-size: 14px;
  font-weight: 500;
}

.ssr-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  background-color: var(--color-bg-2);
  border: 1px solid var(--color-border-1);
  border-radius: 4px;
  color: var(--color-text-3);
}
</style>
