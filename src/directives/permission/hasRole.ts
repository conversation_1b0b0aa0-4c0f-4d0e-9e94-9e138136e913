import type { Directive, DirectiveBinding } from 'vue'
import { useUserStore } from '@/stores'

/**
 * @desc v-role 角色权限处理
 * @desc 使用 v-role="['admin', 'user]"
 */
function checkRole(el: HTMLElement, binding: DirectiveBinding) {
  // 在 SSR 环境下跳过角色检查
  if (typeof window === 'undefined') {
    return
  }

  const userStore = useUserStore()
  const { value } = binding
  const super_admin = 'role_admin'
  if (value && Array.isArray(value) && value.length) {
    const roleValues: string[] = value
    const hasRole = userStore.roles.some((role) => {
      return super_admin === role || roleValues.includes(role)
    })
    if (!hasRole) {
      el.parentNode && el.parentNode.removeChild(el)
    }
  } else {
    throw new Error(`need role! Like v-role="['admin','user']"`)
  }
}

const directive: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    checkRole(el, binding)
  },
  updated(el: HTMLElement, binding: DirectiveBinding) {
    checkRole(el, binding)
  },
  // 添加 SSR 支持
  getSSRProps(_binding: DirectiveBinding) {
    // 在 SSR 环境下，我们不进行角色检查，返回空对象
    return {}
  },
}

export default directive
