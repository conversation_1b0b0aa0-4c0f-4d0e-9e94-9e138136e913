import type { Directive, DirectiveBinding } from 'vue'
import { useUserStore } from '@/stores'

/**
 * @desc v-has 操作权限处理
 * @desc 使用 v-has="['system:user:create']"
 */
function checkPermission(el: HTMLElement, binding: DirectiveBinding) {
  // 在 SSR 环境下跳过权限检查
  if (typeof window === 'undefined') {
    return
  }

  const userStore = useUserStore()
  const { value } = binding
  const all_permission = '*:*:*'

  if (value && Array.isArray(value) && value.length) {
    const permissionValues: string[] = value
    const hasPermission = userStore.permissions.some((perm) => {
      return all_permission === perm || permissionValues.includes(perm)
    })
    if (!hasPermission) {
      el.parentNode && el.parentNode.removeChild(el)
    }
  } else {
    throw new Error(`need permission! Like v-has="['home:btn:edit','home:btn:delete']"`)
  }
}

const directive: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    checkPermission(el, binding)
  },
  updated(el: HTMLElement, binding: DirectiveBinding) {
    checkPermission(el, binding)
  },
  // 添加 SSR 支持
  getSSRProps(_binding: DirectiveBinding) {
    // 在 SSR 环境下，我们不进行权限检查，返回空对象
    return {}
  },
}

export default directive
