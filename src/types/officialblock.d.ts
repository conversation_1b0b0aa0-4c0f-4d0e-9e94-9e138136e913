declare module 'officialblock' {
  import type { App, Plugin, Component } from 'vue'

  // 组件类型定义
  export interface ComponentProps {
    modelValue?: string
    size?: 'small' | 'medium' | 'large'
    disabled?: boolean
  }

  export interface ComponentEmits {
    (e: 'update:modelValue', value: string): void
    (e: 'change', value: string): void
  }

  export interface ComponentSlots {
    default?: () => any
    header?: (props: { title: string }) => any
    footer?: () => any
  }

  export interface SlideItem {
    id: string | number
    title: string
    image: string
    description?: string
    link?: string
  }

  export interface HeroSlideProps {
    slides?: SlideItem[]
    autoplay?: boolean
    interval?: number
    showDots?: boolean
    showArrows?: boolean
  }

  export interface HeroSlideEmits {
    (e: 'slide-change', index: number): void
    (e: 'slide-click', slide: SlideItem): void
  }

  // 组件声明
  export const ArticleList: Component<ComponentProps, ComponentEmits, ComponentSlots>
  export const HeroSlide: Component<HeroSlideProps, HeroSlideEmits>

  // 插件声明
  export const ArticleListPlugin: Plugin
  export const HeroSlidePlugin: Plugin

  // 主插件
  const OfficialBlock: Plugin & {
    install: (app: App) => void
  }

  export default OfficialBlock
}

declare module 'officialblock/style.css' {
  const content: string
  export default content
}
