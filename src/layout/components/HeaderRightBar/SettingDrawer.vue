<template>
  <a-drawer v-model:visible="visible" title="项目配置" width="300px" unmount-on-close :footer="false">
    <a-space :size="15" direction="vertical" fill>
      <a-alert v-if="settingOpen" :show-icon="false" type="info">
        「复制配置」按钮，并将配置粘贴到 src/config/settings.ts 文件中。
      </a-alert>
      <a-divider v-if="settingOpen" orientation="center">系统布局</a-divider>
      <a-row v-if="settingOpen" justify="center">
        <a-space>
          <a-badge>
            <template #content>
              <icon-check-circle-fill
                v-if="appStore.layout === 'left'" style="color: rgb(var(--success-6))"
                :size="16"
              ></icon-check-circle-fill>
            </template>
            <LayoutItem mode="left" @click="appStore.layout = 'left'"></LayoutItem>
            <p class="layout-text">默认布局</p>
          </a-badge>
          <a-badge>
            <template #content>
              <icon-check-circle-fill
                v-if="appStore.layout === 'mix'" :size="16"
                style="color: rgb(var(--success-6))"
              ></icon-check-circle-fill>
            </template>
            <LayoutItem mode="mix" @click="appStore.layout = 'mix'"></LayoutItem>
            <p class="layout-text">混合布局</p>
          </a-badge>
        </a-space>
      </a-row>

      <a-divider orientation="center">系统主题</a-divider>
      <a-row justify="center">
        <ColorPicker
          theme="dark" :color="appStore.themeColor" :sucker-hide="true" :colors-default="defaultColorList"
          @change-color="changeColor"
        ></ColorPicker>
      </a-row>

      <a-divider v-if="settingOpen" orientation="center">界面显示</a-divider>

      <a-descriptions v-if="settingOpen" :column="1" :align="{ value: 'right' }" :value-style="{ paddingRight: 0 }">
        <a-descriptions-item label="页签显示">
          <a-switch v-model="appStore.tab" />
        </a-descriptions-item>
        <a-descriptions-item label="页签风格">
          <a-select
            v-model="appStore.tabMode" placeholder="请选择" :options="tabModeList" :disabled="!appStore.tab"
            :trigger-props="{ autoFitPopupMinWidth: true }" :style="{ width: '120px' }"
          >
          </a-select>
        </a-descriptions-item>
        <a-descriptions-item label="动画显示">
          <a-switch v-model="appStore.animate" />
        </a-descriptions-item>
        <a-descriptions-item label="动画显示">
          <a-select
            v-model="appStore.animateMode" placeholder="请选择" :options="animateModeList"
            :disabled="!appStore.animate" :style="{ width: '120px' }"
          >
          </a-select>
        </a-descriptions-item>
        <a-descriptions-item label="深色菜单">
          <a-switch v-model="appStore.menuDark" />
        </a-descriptions-item>
        <a-descriptions-item label="手风琴效果">
          <a-switch v-model="appStore.menuAccordion" />
        </a-descriptions-item>
        <a-descriptions-item label="版权显示">
          <a-switch v-model="appStore.copyrightDisplay" />
        </a-descriptions-item>
        <a-descriptions-item label="水印">
          <a-switch v-model="appStore.isOpenWatermark" />
        </a-descriptions-item>
        <a-descriptions-item label="水印信息">
          <a-input v-model="appStore.watermark" placeholder="留空则显示用户名" />
        </a-descriptions-item>
      </a-descriptions>

      <a-divider orientation="center">其它</a-divider>
      <a-descriptions :column="1" :align="{ value: 'right' }" :value-style="{ paddingRight: 0 }">
        <a-descriptions-item label="色弱模式">
          <a-switch v-model="appStore.enableColorWeaknessMode" />
        </a-descriptions-item>
        <a-descriptions-item v-if="settingOpen" label="哀悼模式">
          <a-switch v-model="appStore.enableMourningMode" />
        </a-descriptions-item>
      </a-descriptions>
      <a-space v-if="settingOpen" direction="vertical" fill>
        <a-button type="primary" long @click="copySettings">
          <template #icon>
            <icon-copy />
          </template>
          复制配置
        </a-button>
      </a-space>
    </a-space>
  </a-drawer>
</template>

<script setup lang="ts">
import { ColorPicker } from 'vue-color-kit'
import 'vue-color-kit/dist/vue-color-kit.css'
import { useClipboard } from '@vueuse/core'
import { Message } from '@arco-design/web-vue'
import LayoutItem from './components/LayoutItem.vue'
import { useAppStore } from '@/stores'

defineOptions({ name: 'SettingDrawer' })
const appStore = useAppStore()
const visible = ref(false)
const settingOpen = JSON.parse(import.meta.env.VITE_APP_SETTING)
const tabModeList: App.TabItem[] = [
  { label: '卡片', value: 'card' },
  { label: '间隔卡片', value: 'card-gutter' },
  { label: '圆角', value: 'rounded' },
]

const animateModeList: App.AnimateItem[] = [
  { label: '默认', value: 'zoom-fade' },
  { label: '滑动', value: 'fade-slide' },
  { label: '渐变', value: 'fade' },
  { label: '底部滑出', value: 'fade-bottom' },
  { label: '缩放消退', value: 'fade-scale' },
]

const open = () => {
  visible.value = true
}

// 默认显示的主题色列表
const defaultColorList = [
  '#165DFF',
  '#409EFF',
  '#18A058',
  '#2d8cf0',
  '#007AFF',
  '#5ac8fa',
  '#5856D6',
  '#536dfe',
  '#9c27b0',
  '#AF52DE',
  '#0096c7',
  '#00C1D4',
  '#43a047',
  '#e53935',
  '#f4511e',
  '#6d4c41',
]

interface ColorObj {
  hex: string
  hsv: { h: number, s: number, v: number }
  rgba: { r: number, g: number, b: number, a: number }
}

// 改变主题色
const changeColor = (colorObj: ColorObj) => {
  if (!/^#[0-9A-Z]{6}/i.test(colorObj.hex)) return
  appStore.setThemeColor(colorObj.hex)
}

// 复制配置
const copySettings = () => {
  const settings: App.AppSettings = {
    theme: 'light',
    themeColor: appStore.themeColor,
    tab: appStore.tab,
    tabMode: appStore.tabMode,
    animate: appStore.animate,
    animateMode: appStore.animateMode,
    menuCollapse: appStore.menuCollapse,
    menuAccordion: appStore.menuAccordion,
    menuDark: appStore.menuDark,
    copyrightDisplay: appStore.copyrightDisplay,
    layout: appStore.layout,
    isOpenWatermark: appStore.isOpenWatermark,
    watermark: appStore.watermark,
    enableColorWeaknessMode: appStore.enableColorWeaknessMode,
    enableMourningMode: appStore.enableMourningMode,
  }

  const settingJson = JSON.stringify(settings, null, 2)
  const { isSupported, copy } = useClipboard({ source: settingJson })
  if (isSupported) {
    copy(settingJson)
    Message.success({ content: '复制成功!' })
  } else {
    Message.error({ content: '请检查浏览器权限是否开启' })
  }
}

defineExpose({ open })
</script>

<style scoped lang="scss">
:deep(.arco-descriptions-item-label-block) {
  color: var(--color-text-1);
}

.layout-text {
  font-size: 12px;
  text-align: center;
  color: var(--color-text-2);
  margin-top: 4px;
}
</style>
