import { createApp } from 'vue'
import ArcoVue, { Card, Drawer, Modal } from '@arco-design/web-vue'
import ArcoVueIcon from '@arco-design/web-vue/es/icon'
import App from './App.vue'
import { createRouterInstance } from './router'
import { createPinia } from 'pinia'
import directives from './directives'
import i18n from './locale'
import { setHasRouteFlag } from './router/guard'

// 导入样式
import '@/styles/arco-ui/index.less'
import 'animate.css/animate.min.css'
import '@/styles/css/transition.css'
import '@/styles/index.scss'

// 支持SVG（仅客户端）
import 'virtual:svg-icons-register'

const app = createApp(App)
const router = createRouterInstance()
const pinia = createPinia()

// 配置组件默认属性
Card.props.bordered = false

// 注册插件
app.use(router)
app.use(pinia)
app.use(i18n)
app.use(ArcoVue)
app.use(ArcoVueIcon)
app.use(directives)

// 设置上下文
Modal._context = app._context
Drawer._context = app._context

// 添加全局调试函数（仅开发环境）
if (import.meta.env.DEV) {
  ;(window as any).__i18n__ = i18n
  ;(window as any).__changeLocale__ = (locale: string) => {
    i18n.global.locale.value = locale
    localStorage.setItem('continew-locale', locale)
    console.log('语言已切换到:', locale)
  }
}

// 检查 SSR 状态
const ssrState = (window as any).__SSR_STATE__
console.log('Client: SSR State:', ssrState)

// 客户端动态路由恢复逻辑
const restoreDynamicRoutes = async () => {
  try {
    const { useRouteStore } = await import('./stores')
    const { getToken } = await import('./utils/auth')
    const routeStore = useRouteStore()

    // 检查是否有 token
    const token = getToken()
    console.log('Client: Current token:', token)

    if (token) {
      console.log('Client: Loading dynamic routes...')
      const accessRoutes = await routeStore.generateRoutes()

      let restoredCount = 0
      accessRoutes.forEach((route) => {
        // 简单检查是否为 HTTP 路径
        const isHttpPath = route.path.startsWith('http://') || route.path.startsWith('https://')
        if (!isHttpPath) {
          router.addRoute(route)
          restoredCount++
          console.log(`Client: Added route: ${route.name} (${route.path})`)
        }
      })

      setHasRouteFlag(true)
      console.log(`Client: Successfully loaded ${restoredCount} dynamic routes`)
    } else {
      console.log('Client: No token found, skipping dynamic route loading')
    }
  } catch (error) {
    console.error('Client: Failed to load dynamic routes:', error)
  }
}

// 总是尝试恢复动态路由（无论是否有 SSR 状态）
if (ssrState && ssrState.routesLoaded) {
  console.log('Client: SSR routes detected, restoring from SSR state')
} else {
  console.log('Client: No SSR routes, checking for client-side token')
}

// 异步加载动态路由
restoreDynamicRoutes()

// 等待路由准备就绪后挂载应用
router.isReady().then(() => {
  app.mount('#app', true)
})
