import { createApp } from 'vue'
import ArcoVue, { Card, Drawer, Modal } from '@arco-design/web-vue'
import ArcoVueIcon from '@arco-design/web-vue/es/icon'
import App from './App.vue'
import { createRouterInstance } from './router'
import { createPinia } from 'pinia'
import directives from './directives'
import i18n from './locale'

// 导入样式
import '@/styles/arco-ui/index.less'
import 'animate.css/animate.min.css'
import '@/styles/css/transition.css'
import '@/styles/index.scss'

// 支持SVG（仅客户端）
import 'virtual:svg-icons-register'

const app = createApp(App)
const router = createRouterInstance()
const pinia = createPinia()

// 配置组件默认属性
Card.props.bordered = false

// 注册插件
app.use(router)
app.use(pinia)
app.use(i18n)
app.use(ArcoVue)
app.use(ArcoVueIcon)
app.use(directives)

// 设置上下文
Modal._context = app._context
Drawer._context = app._context

// 添加全局调试函数（仅开发环境）
if (import.meta.env.DEV) {
  ;(window as any).__i18n__ = i18n
  ;(window as any).__changeLocale__ = (locale: string) => {
    i18n.global.locale.value = locale
    localStorage.setItem('continew-locale', locale)
    console.log('语言已切换到:', locale)
  }
}

// 等待路由准备就绪后挂载应用
router.isReady().then(() => {
  app.mount('#app', true)
})
