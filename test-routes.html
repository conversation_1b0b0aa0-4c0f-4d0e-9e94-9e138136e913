<!DOCTYPE html>
<html>
<head>
    <title>路由测试</title>
</head>
<body>
    <h1>动态路由测试</h1>
    <div>
        <button onclick="setToken()">设置Token</button>
        <button onclick="clearToken()">清除Token</button>
        <button onclick="testRoute()">测试路由</button>
    </div>
    <div id="log"></div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
        }

        function setToken() {
            localStorage.setItem('token', 'test-token-' + Date.now());
            log('Token已设置');
        }

        function clearToken() {
            localStorage.removeItem('token');
            log('Token已清除');
        }

        function testRoute() {
            const token = localStorage.getItem('token');
            log('当前Token: ' + (token || '无'));
            
            if (token) {
                log('尝试访问 /system/user');
                window.location.href = '/system/user';
            } else {
                log('请先设置Token');
            }
        }
    </script>
</body>
</html>
