# i18n 迁移指南

## 问题解决

✅ **登录页面正常工作** - 说明 i18n 基础配置正确
❌ **其他页面不工作** - 需要为每个组件添加 i18n 支持

## 解决方案

### 1. 为组件添加 i18n 支持的步骤

#### 步骤 1：导入 useI18n
```vue
<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
// 其他代码...
</script>
```

#### 步骤 2：替换硬编码文本
```vue
<!-- 之前 -->
<h1>我的项目</h1>
<button>确认</button>

<!-- 之后 -->
<h1>{{ t('dashboard.project.title') }}</h1>
<button>{{ t('common.confirm') }}</button>
```

#### 步骤 3：在语言文件中添加翻译键
**src/locale/zh-CN.ts**
```typescript
export default {
  'dashboard.project.title': '我的项目',
  // ...
}
```

**src/locale/en-US.ts**
```typescript
export default {
  'dashboard.project.title': 'My Projects',
  // ...
}
```

### 2. 已修复的组件示例

#### ✅ Welcome 组件
- 文件：`src/views/dashboard/workplace/components/Welcome.vue`
- 修改：添加了 `useI18n` 导入和 `t('dashboard.welcome.motto')`

#### ✅ Project 组件
- 文件：`src/views/dashboard/workplace/components/Project.vue`
- 修改：添加了 `useI18n` 导入和多个翻译键

### 3. 快速修复其他组件

#### 查找需要修复的组件
```bash
# 查找包含中文文本的 Vue 文件
grep -r "[\u4e00-\u9fa5]" src/views --include="*.vue"
```

#### 常见需要替换的文本模式
- 标题文本：`title="标题"` → `:title="t('module.title')"`
- 按钮文本：`<button>按钮</button>` → `<button>{{ t('common.button') }}</button>`
- 提示文本：`content="提示"` → `:content="t('common.tip')"`
- 占位符：`placeholder="请输入"` → `:placeholder="t('common.placeholder')"`

### 4. 批量修复脚本

创建一个简单的查找脚本来识别需要修复的文件：

```bash
#!/bin/bash
echo "查找包含中文但未使用 i18n 的 Vue 文件："
find src/views -name "*.vue" -exec grep -l "[\u4e00-\u9fa5]" {} \; | while read file; do
  if ! grep -q "useI18n\|vue-i18n" "$file"; then
    echo "需要修复: $file"
  fi
done
```

### 5. 优先修复的组件类型

1. **导航和菜单组件** - 用户最常看到
2. **表单组件** - 包含大量文本
3. **表格组件** - 列标题和操作按钮
4. **对话框和模态框** - 确认/取消按钮
5. **通知和消息组件** - 提示文本

### 6. 测试修复结果

修复组件后，访问对应页面：
1. 切换语言查看是否正常显示
2. 检查浏览器控制台是否有错误
3. 确认所有文本都已国际化

### 7. 常用翻译键参考

```typescript
// 通用操作
'common.confirm': '确认' / 'Confirm'
'common.cancel': '取消' / 'Cancel'
'common.save': '保存' / 'Save'
'common.delete': '删除' / 'Delete'
'common.edit': '编辑' / 'Edit'
'common.add': '新增' / 'Add'
'common.search': '搜索' / 'Search'
'common.reset': '重置' / 'Reset'

// 表单
'form.required': '必填项' / 'Required'
'form.placeholder': '请输入' / 'Please enter'

// 表格
'table.action': '操作' / 'Action'
'table.status': '状态' / 'Status'
'table.createTime': '创建时间' / 'Create Time'

// 消息
'message.success': '操作成功' / 'Success'
'message.error': '操作失败' / 'Error'
'message.confirm': '确认操作' / 'Confirm'
```

### 8. 注意事项

1. **保持翻译键的一致性**：使用统一的命名规范
2. **避免过度细分**：相同含义的文本使用相同的键
3. **参数化动态内容**：`t('message.total', { count: 10 })`
4. **测试所有语言**：确保切换后显示正常
5. **渐进式迁移**：优先修复重要页面

### 9. 下一步计划

1. 修复主要的仪表盘组件 ✅
2. 修复系统管理相关页面
3. 修复用户管理相关页面
4. 修复其他业务页面
5. 添加更多语言支持（如需要）

## 当前状态

- ✅ i18n 基础配置完成
- ✅ 登录页面支持语言切换
- ✅ 头部导航栏支持语言切换
- ✅ Welcome 组件已修复
- ✅ Project 组件已修复
- 🔄 其他组件待修复

继续按照这个指南修复其他组件，很快就能实现全站的国际化支持！
