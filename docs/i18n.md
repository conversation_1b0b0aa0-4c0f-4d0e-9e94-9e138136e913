# 国际化 (i18n) 使用指南

本项目已集成 Vue I18n 国际化功能，支持中文和英文两种语言。

## 功能特性

- ✅ 支持中文 (zh-CN) 和英文 (en-US)
- ✅ 语言切换组件
- ✅ 本地存储语言偏好
- ✅ 参数化翻译
- ✅ 组合式 API 支持
- ✅ TypeScript 支持

## 目录结构

```
src/
├── locale/                 # 国际化配置
│   ├── index.ts            # i18n 实例配置
│   ├── zh-CN.ts           # 中文语言包
│   └── en-US.ts           # 英文语言包
├── components/
│   └── LocaleSwitch/      # 语言切换组件
├── hooks/
│   └── useLocale.ts       # 语言切换 Hook
└── utils/
    └── i18n.ts            # i18n 工具函数
```

## 基本使用

### 1. 在组件中使用翻译

```vue
<template>
  <div>
    <h1>{{ t('login.title') }}</h1>
    <button>{{ t('common.confirm') }}</button>
    <button>{{ t('common.cancel') }}</button>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
</script>
```

### 2. 参数化翻译

```vue
<template>
  <div>
    <p>{{ t('common.total', { total: 100 }) }}</p>
    <p>{{ t('pagination.pageSize', { size: 20 }) }}</p>
  </div>
</template>
```

### 3. 使用语言切换组件

```vue
<template>
  <LocaleSwitch />
</template>

<script setup lang="ts">
import LocaleSwitch from '@/components/LocaleSwitch/index.vue'
</script>
```

### 4. 使用 useLocale Hook

```vue
<script setup lang="ts">
import useLocale from '@/hooks/useLocale'

const { currentLocale, changeLocale, getLocaleLabel } = useLocale()

// 切换到中文
const switchToChinese = () => {
  changeLocale('zh-CN')
}

// 切换到英文
const switchToEnglish = () => {
  changeLocale('en-US')
}
</script>
```

### 5. 在 JavaScript 中使用翻译

```typescript
import { $t } from '@/utils/i18n'

// 在非组件环境中使用
const message = $t('message.success')
console.log(message)
```

## 添加新的翻译

### 1. 在语言文件中添加翻译键

**src/locale/zh-CN.ts**
```typescript
export default {
  // 新增翻译
  'user.profile': '个人资料',
  'user.settings': '用户设置',
  // ...
}
```

**src/locale/en-US.ts**
```typescript
export default {
  // 新增翻译
  'user.profile': 'User Profile',
  'user.settings': 'User Settings',
  // ...
}
```

### 2. 在组件中使用新翻译

```vue
<template>
  <div>
    <h2>{{ t('user.profile') }}</h2>
    <button>{{ t('user.settings') }}</button>
  </div>
</template>
```

## 翻译键命名规范

建议使用以下命名规范：

- `common.*` - 通用翻译（确认、取消、保存等）
- `menu.*` - 菜单相关翻译
- `navbar.*` - 导航栏相关翻译
- `login.*` - 登录页面相关翻译
- `message.*` - 消息提示相关翻译
- `table.*` - 表格相关翻译
- `pagination.*` - 分页相关翻译
- `upload.*` - 文件上传相关翻译
- `validate.*` - 表单验证相关翻译

## 最佳实践

1. **保持翻译键的一致性**：使用统一的命名规范
2. **避免硬编码文本**：所有用户可见的文本都应该使用翻译
3. **参数化动态内容**：使用参数传递动态值
4. **测试多语言**：确保在不同语言下界面显示正常
5. **及时更新翻译**：添加新功能时同步更新所有语言的翻译

## 示例页面

访问 `/test/i18n-test` 查看 i18n 功能的基本示例。
访问 `/demo/i18n` 查看完整的 i18n 功能演示。

## 扩展支持更多语言

如需支持更多语言，请按以下步骤操作：

1. 在 `src/locale/` 目录下创建新的语言文件，如 `ja-JP.ts`
2. 在 `src/locale/index.ts` 中导入并配置新语言
3. 在 `src/hooks/useLocale.ts` 中添加新语言选项
4. 更新 `src/utils/i18n.ts` 中的可用语言列表

## 注意事项

- 语言偏好会自动保存到 localStorage
- 页面刷新后会自动恢复用户的语言选择
- 默认语言为中文 (zh-CN)
- 如果翻译键不存在，会显示键名本身
